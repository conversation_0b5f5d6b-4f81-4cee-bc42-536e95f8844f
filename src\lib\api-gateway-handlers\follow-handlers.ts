import { User } from "@/services/UserInterface";
import { FieldValue, getFirestore } from "firebase-admin/firestore";
import { NotificationHandlerManager } from "./notification-handlers";
import { NotificationEvents } from "@/services/notificationService";
import { initAdmin } from "../../../firebaseAdminConfig";

export class FollowerHandlerManager {
  private USERS_COLLECTION = "users";

  static instance: FollowerHandlerManager | null = null;

  private constructor() {}

  static getInstance() {
    if (!this.instance) {
      this.instance = new FollowerHandlerManager();
    }
    return this.instance;
  }

  private async getFollowers(userId: string): Promise<User[]> {
    await initAdmin();
    const db = getFirestore();

    try {
      const followersSnap = await db
        .collection(this.USERS_COLLECTION)
        .where("bookmarks", "array-contains", userId)
        .get();

      return followersSnap.docs.map(
        (doc) =>
          ({
            id: doc.id,
            ...doc.data(),
          }) as User
      );
    } catch (error) {
      console.error("Error fetching followers:", error);
      return [];
    }
  }
  private _sanitizeUserPayload(data: User) {
    const { stripe_id, email, basketOrdersCount, password, ...safeData } = data;

    return safeData;
  }

  async GetFollowersByUserId(userId: string) {
    try {
      //  await initAdmin();
      if (!userId || typeof userId !== "string" || userId.trim() === "") {
        console.error("Invalid userId provided:", userId);
        return [];
      }

      const db = getFirestore();

      const userRef = db.collection(this.USERS_COLLECTION).doc(userId);
      const userSnap = await userRef.get();
      

      if (!userSnap.exists) {
        throw new Error("user_not_found");
      }

      const followers: Array<Omit<User, "stripe_id" | "email" | "basketOrdersCount" | "password">> =
        (await this.getFollowers(userId))?.map((c) => {
          return this._sanitizeUserPayload(c);
        });

      return followers;
    } catch (error) {
      console.error("Error getting followers:", error);
      return [];
    }
  }

  async GetFollowingsByUserId(userId: string) {
    try {
      if (!userId || typeof userId !== "string" || userId.trim() === "") {
        console.error("Invalid userId provided:", userId);
        return [];
      }

      const db = getFirestore();

      const userSnap = await db.collection(this.USERS_COLLECTION).doc(userId).get();
      if (!userSnap.exists) throw new Error("user_not_found");

      const userData = userSnap.data();
      const followingIds: string[] = userData?.bookmarks || [];
      if (followingIds.length === 0) return [];

      const batchSize = 10; // Firestore 'in' query supports max 10 values
      const chunks: string[][] = [];

      for (let i = 0; i < followingIds.length; i += batchSize) {
        chunks.push(followingIds.slice(i, i + batchSize));
      }

      let followingsData: User[] = [];

      for (const chunk of chunks) {
        const snapshot = await db
          .collection(this.USERS_COLLECTION)
          .where("__name__", "in", chunk)
          .get();

        snapshot.docs.forEach((doc) => {
          followingsData.push({ id: doc.id, ...doc.data() } as User);
        });
      }

      let resp: Array<Omit<User, "stripe_id" | "email" | "basketOrdersCount" | "password">> =
        followingsData?.map((c) => this._sanitizeUserPayload(c));

      return resp;
    } catch (error) {
      console.error("Error getting followings:", error);
      return [];
    }
  }

  // edit

  async FollowByUserId({
    src_id,
    dest_id,
  }: {
    src_id: string;
    dest_id: string;
  }): Promise<"success" | "failed"> {
    try {
      if (!src_id || src_id === dest_id) {
        console.error("Invalid follow request:", { src_id, dest_id });
        return "failed";
      }

      const db = getFirestore();

      const srcRef = db.collection(this.USERS_COLLECTION).doc(src_id);

      // Add dest_id to bookmarks array
      await srcRef.update({
        bookmarks: FieldValue.arrayUnion(dest_id),
      });

      // admin-sdk
      await NotificationHandlerManager.getInstance().CreateNotification({
        payload: {
          src_id,
          dest_id,
          event: NotificationEvents.FOLLOW,
        },
      });

      return "success";
    } catch (error) {
      console.error("Error following user:", error);
      return "failed";
    }
  }

  async UnfollowByUserId({
    src_id,
    dest_id,
  }: {
    src_id: string;
    dest_id: string;
  }): Promise<"success" | "failed"> {
    try {
      if (!src_id || src_id === dest_id) {
        console.error("Invalid unfollow request:", { src_id, dest_id });
        return "failed";
      }

      const db = getFirestore();
      const srcRef = db.collection(this.USERS_COLLECTION).doc(src_id);

      // Remove dest_id from bookmarks array
      await srcRef.update({
        bookmarks: FieldValue.arrayRemove(dest_id),
      });

      return "success";
    } catch (error) {
      console.error("Error unfollowing user:", error);
      return "failed";
    }
  }
}
