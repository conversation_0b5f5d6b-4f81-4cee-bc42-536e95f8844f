"use client";

import React from 'react';
import Link from 'next/link';
import { But<PERSON> } from './ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from './ui/card';
import { 
  CreditCard, 
  Settings, 
  DollarSign, 
  User, 
  ShoppingCart,
  Building,
  ArrowRight,
  Sparkles
} from 'lucide-react';

export const ConnectNavigation: React.FC = () => {
  const connectFeatures = [
    {
      title: 'Stripe',
      description: 'Complete seller dashboard with embedded Connect components',
      href: '/payment/connect-dashboard',
      icon: Building,
      color: 'bg-blue-500',
    },
    {
      title: 'Seller Onboarding',
      description: 'Embedded onboarding flow without redirects',
      href: '/payment/seller-onboard',
      icon: User,
      color: 'bg-green-500',
    },
    {
      title: 'Connect Demo',
      description: 'Interactive demo of all Connect embedded components',
      href: '/payment/connect-demo',
      icon: Sparkles,
      color: 'bg-purple-500',
    },
    {
      title: 'Enhanced Payment',
      description: 'Payment forms with Connect integration',
      href: '/payment/enhanced-demo',
      icon: CreditCard,
      color: 'bg-orange-500',
    },
  ];

  return (
    <div className="space-y-6">
      <div className="text-center">
        <h2 className="text-2xl font-bold text-gray-900 mb-2">
          Stripe Connect Embedded Components
        </h2>
        <p className="text-gray-600 max-w-2xl mx-auto">
          Experience the new generation of Stripe Connect with embedded components 
          that keep users on your platform without any redirects.
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {connectFeatures.map((feature) => {
          const IconComponent = feature.icon;
          return (
            <Card key={feature.href} className="hover:shadow-lg transition-shadow">
              <CardHeader>
                <div className="flex items-center space-x-3">
                  <div className={`p-2 rounded-lg ${feature.color}`}>
                    <IconComponent className="h-5 w-5 text-white" />
                  </div>
                  <div>
                    <CardTitle className="text-lg">{feature.title}</CardTitle>
                    <CardDescription>{feature.description}</CardDescription>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <Link href={feature.href}>
                  <Button className="w-full group">
                    Try it out
                    <ArrowRight className="h-4 w-4 ml-2 group-hover:translate-x-1 transition-transform" />
                  </Button>
                </Link>
              </CardContent>
            </Card>
          );
        })}
      </div>

      <Card className="bg-gradient-to-r from-blue-50 to-purple-50 border-blue-200">
        <CardHeader>
          <CardTitle className="flex items-center text-blue-900">
            <Sparkles className="h-5 w-5 mr-2" />
            What's New with Connect Embedded Components
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 className="font-semibold text-blue-900 mb-2">Key Benefits</h4>
              <ul className="text-sm text-blue-800 space-y-1">
                <li>✅ No redirects - seamless user experience</li>
                <li>✅ Consistent branding across your platform</li>
                <li>✅ Mobile-optimized and responsive design</li>
                <li>✅ Real-time updates and notifications</li>
                <li>✅ Automatic compliance and security</li>
              </ul>
            </div>
            <div>
              <h4 className="font-semibold text-blue-900 mb-2">Available Components</h4>
              <ul className="text-sm text-blue-800 space-y-1">
                <li>🏢 Account Onboarding</li>
                <li>⚙️ Account Management</li>
                <li>💳 Payment Management</li>
                <li>💰 Payout Management</li>
                <li>📊 Balance Overview</li>
                <li>🔔 Notification Banner</li>
              </ul>
            </div>
          </div>
          
          <div className="bg-white p-4 rounded-lg border border-blue-200">
            <h4 className="font-semibold text-blue-900 mb-2">Migration from Legacy Components</h4>
            <p className="text-sm text-blue-800">
              The new embedded components replace the previous redirect-based flows. 
              Your existing Stripe accounts will work seamlessly with the new components.
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default ConnectNavigation;
