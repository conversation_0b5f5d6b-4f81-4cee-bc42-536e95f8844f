// update script
// export const updateEmptyUserNames = async () => {
//   const { db } = await initFirebase();

//   // 1. Get all users with profile_name "" or "Profile Name"
//   const usersRef = collection(db, "users");
//   // const q = query(usersRef, where("profile_name", "in", ["", "Profile Name"]));
// //   const q = query(
// //   usersRef,
// //   where("profile_name", "==", null) // matches docs with null, but not truly missing
// // );
// const snapshot = await getDocs(usersRef);
// const users = snapshot.docs.filter(doc => {
//   const name = doc.get("profile_name");
//   return name === "" || name === "Profile Name" || name === undefined;
// });
//   // const snapshot = await getDocs(q);

  
//   console.log(`Found ${users.length} users to update.`);

//   for (const userDoc of users) {
//     const userId = userDoc.id;

//     // // 2. Generate a unique user_name
//     const uniqueUserName = await getUniqueUserName();
//     console.log({userId , uniqueUserName});
    

//     // // // 3. Update the profile_name
//     await updateDoc(doc(db, "users", userId), {
//       profile_name: uniqueUserName,
//     });

//     console.log(`✅ Updated user ${userId} to profile_name: ${uniqueUserName}`);
//   }
// };
