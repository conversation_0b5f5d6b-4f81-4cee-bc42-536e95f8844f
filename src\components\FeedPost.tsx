"use client";
import { PostsQuery } from "@/graphql/test/generated";
import sanitizeDStorageUrl from "@/lib/sanatizeUrl";
import LazyMedia from "./LazyMedia";

export default function FeedPost({
  publication,
  data,
}: {
  publication: PostsQuery["posts"]["items"][0];
  data: any;
}) {
  // Helper function to get media source and type
  const getMediaInfo = () => {
    if (publication?.__typename !== "Post") return null;

    const metadata = publication?.metadata;
    if (metadata?.__typename === "ImageMetadata" && metadata?.image?.item) {
      return {
        src: sanitizeDStorageUrl(metadata.image.item),
        type: "image" as const,
      };
    }

    if (metadata?.__typename === "VideoMetadata" && metadata?.video?.item) {
      return {
        src: sanitizeDStorageUrl(metadata.video.item),
        type: "video" as const,
      };
    }

    return null;
  };

  const mediaInfo = getMediaInfo();

  return (
    <div key={publication.id} className="flex flex-col">
      <div className="flex flex-col gap-8">
        <div className="flex font-bold">{publication?.author?.username?.localName}</div>
      </div>

      {mediaInfo && (
        <div className="flex">
          <LazyMedia
            src={mediaInfo.src}
            type={mediaInfo.type}
            alt=""
            className="w-[490px] h-[250px] rounded-full object-cover"
          />
        </div>
      )}
    </div>
  );
}
