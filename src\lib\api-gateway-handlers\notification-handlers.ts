import { Notifications, UserNotificationSummary } from "@/services/notificationService";
import { getFirestore, FieldValue, Timestamp, WriteBatch } from "firebase-admin/firestore";

export class NotificationHandlerManager {
  private NOTIFICATION_COLLECTION = "notifications";
  private USER_NOTIFICATION_SUMMARY = "user_notification_summary";
  private USERS_COLLECTION = "users";
  private BATCH_LIMIT = 500;

  static instance: NotificationHandlerManager | null = null;

  private constructor() {}

  static getInstance() {
    if (!this.instance) {
      this.instance = new NotificationHandlerManager();
    }
    return this.instance;
  }

  private _sanitizeUserPayload(doc: FirebaseFirestore.QueryDocumentSnapshot): Record<string, any> {
    const data = doc.data();

    if (!data) return { id: doc.id };

    // Strip sensitive fields
    const { stripe_id, email, basketOrdersCount, password, ...safeData } = data;

    return { id: doc.id, ...safeData };
  }
  private async _fetchUsersByIds(db: FirebaseFirestore.Firestore, userIds: string[]) {
    const chunks = [];
    for (let i = 0; i < userIds.length; i += 10) {
      chunks.push(userIds.slice(i, i + 10));
    }

    const userDocs: any[] = [];

    for (const chunk of chunks) {
      const snapshot = await db
        .collection(this.USERS_COLLECTION)
        .where("__name__", "in", chunk)
        .get();

      snapshot.forEach((doc) => {
        userDocs.push(this._sanitizeUserPayload(doc));
      });
    }

    return userDocs;
  }
  private async IncrementUnreadCount({ user_id }: { user_id: string }) {
    const db = getFirestore();
    const userRef = db.collection(this.USER_NOTIFICATION_SUMMARY).doc(user_id);
    await userRef.set({ unread_count: FieldValue.increment(1) }, { merge: true });
  }
  private async ResetUserNotificationSummary({ user_id }: { user_id: string }) {
    try {
      const db = getFirestore();
      const summaryRef = db.collection(this.USER_NOTIFICATION_SUMMARY).doc(user_id);

      await summaryRef.update({
        unread_count: 0,
        last_updated: FieldValue.serverTimestamp(),
      });
    } catch (error) {
      console.error("Failed to reset UserNotificationSummary:", error);
      throw new Error("ResetUserNotificationSummaryFailed");
    }
  }

  async CreateNotification({
    payload,
    check_duplicate = true,
  }: {
    payload: Notifications;
    check_duplicate?: boolean;
  }) {
    try {
      const db = getFirestore();

      const notificationRef = db.collection(this.NOTIFICATION_COLLECTION);

      if (check_duplicate) {
        let queryRef: FirebaseFirestore.Query = notificationRef
          .where("src_id", "==", payload.src_id)
          .where("dest_id", "==", payload.dest_id)
          .where("event", "==", payload.event);

        if (payload.comment !== undefined) {
          queryRef = queryRef.where("comment", "==", payload.comment);
        }
        if (payload.comment_id !== undefined) {
          queryRef = queryRef.where("comment_id", "==", payload.comment_id);
        }
        if (payload.post_id !== undefined) {
          queryRef = queryRef.where("post_id", "==", payload.post_id);
        }
        if (payload.post_url !== undefined) {
          queryRef = queryRef.where("post_url", "==", payload.post_url);
        }
        if (payload.thumbnail_url !== undefined) {
          queryRef = queryRef.where("thumbnail_url", "==", payload.thumbnail_url);
        }

        const dupSnap = await queryRef.limit(1).get();
        if (!dupSnap.empty) {
          return "duplicate";
        }
      }

      const docRef = notificationRef.doc(); // pre-generate ID

      await docRef.set(
        Object.fromEntries(
          Object.entries({
            ...payload,
            id: docRef.id,
            timestamp: FieldValue.serverTimestamp(),
            deleted: false,
            read: false,
          }).filter(([_, v]) => v !== undefined)
        )
      );

      // update unread count (safe server-side)
      await this.IncrementUnreadCount({ user_id: payload.dest_id });

      return "success";
    } catch (error) {
      console.error("CreateNotification failed:", error);
      throw new Error("createNotificationFailed");
    }
  }

  async UserUnreadNotificationCount({ user_id }: { user_id: string }) {
    try {
      const db = getFirestore();
      const summaryRef = db.collection(this.USER_NOTIFICATION_SUMMARY).doc(user_id);

      const snapshot = await summaryRef.get();

      if (!snapshot.exists) return 0;

      const data = snapshot.data() as UserNotificationSummary;
      return data?.unread_count ?? 0;
      // return { success: true, message: data?.unread_count ?? 0 };
    } catch (error) {
      console.error("Failed to fetch unread count:", error);
      throw new Error("GetUserUnreadNotificationCountFailed");
    }
  }

  async GetNotificationsByUserId({ user_id }: { user_id: string }): Promise<Notifications[]> {
    try {
      const db = getFirestore();

      // Step 1: Fetch notifications for this user
      const snapshot = await db
        .collection(this.NOTIFICATION_COLLECTION)
        .where("dest_id", "==", user_id)
        .orderBy("timestamp", "desc")
        .get();

      let notifications: Notifications[] = snapshot.docs.map((doc) => ({
        id: doc.id,
        ...doc.data(),
        // normalize Firestore Timestamp -> ISO string (or millis)
        timestamp:
          doc.data().timestamp instanceof Timestamp
            ? doc.data().timestamp.toDate().toISOString() // or .toMillis()
            : doc.data().timestamp,
      })) as Notifications[];

      if (notifications.length === 0) {
        return [];
      }

      // Step 2: Extract unique src_ids
      const srcIds = [...new Set(notifications.map((n) => n.src_id))];

      // Step 3: Fetch all corresponding user docs in one go
      let srcUsers: FirebaseFirestore.DocumentData[] = await this._fetchUsersByIds(db, srcIds);
      const userMap = new Map(srcUsers.map((u) => [u.id, u]));

      // Step 4: Enrich notifications
      let enrichedNotifications = notifications.map((n) => ({
        ...n,
        src_details: userMap.get(n.src_id) || null,
      }));

      // Step 5: Reset unread summary if needed
      const unreadCount = await this.UserUnreadNotificationCount({ user_id });
      if (unreadCount > 0) {
        await this.ResetUserNotificationSummary({ user_id });
      }
      
      return enrichedNotifications;
    } catch (error) {
      console.error("GetNotificationsByUserId failed:", error);
      throw new Error("GetNotificationsByUserIdFailed");
    }
  }

  async BulkCreateNotificationsAndUpdateUnreadCounts({
    userId,
    followers,
    postData,
    event,
  }: {
    userId: string;
    followers: { id: string }[];
    postData?: { postId?: string; postFile?: string; thumbnailUrl?: string };
    event: string;
  }): Promise<void> {
    try {
      const db = getFirestore();
      const affectedUserIds = new Set<string>();

      let batch: WriteBatch = db.batch();
      let opCount = 0;

      const notificationCollection = db.collection(this.NOTIFICATION_COLLECTION);

      // --- Step 1: Create notifications in batches ---
      for (let i = 0; i < followers.length; i++) {
        const followerId = followers[i].id;

        let notificationPayload: Record<string, any> = {
          src_id: userId,
          dest_id: followerId,
          event,
        };

        if (postData) {
          notificationPayload = {
            ...notificationPayload,
            ...(postData?.postId && { post_id: postData.postId }),
            ...(postData?.postFile && { post_url: postData.postFile }),
            ...(postData?.thumbnailUrl && { thumbnail_url: postData.thumbnailUrl }),
          };
        }

        const docRef = notificationCollection.doc(); // auto-id
        batch.set(docRef, {
          ...notificationPayload,
          id: docRef.id,
          timestamp: FieldValue.serverTimestamp(),
          deleted: false,
        });

        affectedUserIds.add(followerId);
        opCount++;

        if (opCount === this.BATCH_LIMIT) {
          await batch.commit();
          batch = db.batch();
          opCount = 0;
        }
      }

      if (opCount > 0) {
        await batch.commit();
      }

      // --- Step 2: Update unread counts in batches ---
      batch = db.batch();
      opCount = 0;

      for (const user_id of affectedUserIds) {
        const summaryRef = db.collection(this.USER_NOTIFICATION_SUMMARY).doc(user_id);

        batch.set(
          summaryRef,
          {
            unread_count: FieldValue.increment(1),
            last_updated: FieldValue.serverTimestamp(),
          },
          { merge: true }
        );

        opCount++;

        if (opCount === this.BATCH_LIMIT) {
          await batch.commit();
          batch = db.batch();
          opCount = 0;
        }
      }

      if (opCount > 0) {
        await batch.commit();
      }
    } catch (error) {
      console.error("BulkCreateNotificationsAndUpdateUnreadCounts_Failed", error);
      throw new Error("BulkCreateNotificationsAndUpdateUnreadCounts_Failed");
    }
  }

  
  
  
}
