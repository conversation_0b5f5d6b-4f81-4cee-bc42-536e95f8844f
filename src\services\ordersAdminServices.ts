"use server";
import "server-only";

import { initAdmin } from "../../firebaseAdminConfig";
import admin from "firebase-admin";

const ORDERS_COLLECTION = "orders"; // keep in sync with ordersServices

export async function updateOrderAdmin(
  id: string,
  updatedData: Record<string, any>
): Promise<{ success: true } | { success: false; error: string }> {
  try {
    await initAdmin();
    const adb = admin.firestore();
    await adb.collection(ORDERS_COLLECTION).doc(id).set(
      {
        ...updatedData,
        added_at: new Date(),
      },
      { merge: true }
    );
    return { success: true };
  } catch (e) {
    return { success: false, error: "Failed to update order (admin)" };
  }
}

export async function updateOrderStripeDetailsAdmin(params: {
  id: string; // order id
  chargeId?: string;
  transactionId?: string;
}): Promise<"ok"> {
  const { id, chargeId, transactionId } = params;
  await initAdmin();
  const adb = admin.firestore();
  await adb.collection(ORDERS_COLLECTION).doc(id).set(
    {
      ...(chargeId !== undefined ? { chargeId } : {}),
      ...(transactionId !== undefined ? { transactionId } : {}),
    },
    { merge: true }
  );
  return "ok";
}



import { OrderActivityType, OrderInfo, OrderInfoName, OrderStatusType, getOrderInfoDesc, getOrderStatusDesc } from "@/lib/constant";

function generateNextOrderId(n: number): string {
  return `A${String(n + 1).padStart(6, "0")}`;
}

export async function addToOrderStateAdmin(params: {
  id: string;
  dueDate?: Date;
  loggedInUser: string;
  sellerName: string;
  userName: string;
  sendInvoice?: boolean;
}): Promise<{ success: true; idempotent?: boolean } | { success: false; error: string }> {
  const { id, dueDate, loggedInUser, sellerName, userName } = params;

  try {
    await initAdmin();
    const adb = admin.firestore();

    // Fetch order
    const orderRef = adb.collection(ORDERS_COLLECTION).doc(id);
    const orderSnap = await orderRef.get();
    if (!orderSnap.exists) {
      return { success: false, error: "Order not found" };
    }

    const order = orderSnap.data() as any;

    // Idempotency: only proceed if order is still in BASKET
    if (order.status !== "BASKET") {
      return { success: true, idempotent: true };
    }

    // Fetch service
    const serviceRef = adb.collection("services").doc(order.serviceId);
    const serviceSnap = await serviceRef.get();
    if (!serviceSnap.exists) {
      return { success: false, error: "Service not found" };
    }

    const serviceModel: any = serviceSnap.data() || {};

    // Attach selected customizations (if any)
    let customizationsData: any[] = [];
    const selected = Array.isArray(order.selectedCustomizations)
      ? (order.selectedCustomizations as string[])
      : [];

    if (selected.length > 0) {
      const customizationRefs = selected.map((cid) => serviceRef.collection("customizations").doc(cid));
      const snaps = await Promise.all(customizationRefs.map((r) => r.get()));
      customizationsData = snaps
        .filter((s) => s.exists)
        .map((s) => ({ id: s.id, ...(s.data() as any) }));
    }

    // Mirror client-side price adjustments
    serviceModel.servicePrice = serviceModel?.price;
    if (serviceModel?.price !== undefined) {
      serviceModel.price = parseFloat((Number(serviceModel.price) / (1 - 0.16)).toFixed(2)).toString();
    }
    serviceModel.customizationsModels = (customizationsData || []).map((c) => ({
      ...c,
      originalPrice: c.price,
      price: parseFloat((Number(c.price) / (1 - 0.16)).toFixed(2)).toString(),
    }));

    // Generate sequential uniqueId (simple count-based)
    const orderIdsRef = adb.collection("ordersIds");
    const snapshot = await orderIdsRef.get();
    const count = snapshot.size;
    const newUniqueId = generateNextOrderId(count);

    await orderIdsRef.doc(newUniqueId).set({ orderIds: [id] }, { merge: true });

    // Build update payload
    const now = admin.firestore.Timestamp.now();
    const defaultDue = admin.firestore.Timestamp.fromDate(new Date(Date.now() + 48 * 60 * 60 * 1000));

    const updatePayload: Record<string, any> = {
      status: OrderStatusType.NEW,
      uniqueId: newUniqueId,
      serviceModel,
      dueDate: defaultDue,
      added_at: now,
    };

    if (dueDate) {
      updatePayload.specificDueDate = admin.firestore.Timestamp.fromDate(dueDate);
    }

    await orderRef.set(updatePayload, { merge: true });

    // Create activity logs (status update + order info)
    const activityRef = orderRef.collection("activityLog");
    const statusDesc = getOrderStatusDesc({
      status: OrderStatusType.NEW,
      comment: "",
      reason: "Payment completed",
      userName,
      sellerName,
      profileType: "user",
    }) || "";

    const statusLog = {
      orderId: newUniqueId,
      title: OrderStatusType.NEW,
      date: now,
      type: OrderActivityType.orderStatusUpdate,
      from: "user" as const,
      description: statusDesc,
    };

    const infoDesc = getOrderInfoDesc({
      status: OrderInfo.full,
      sellerName,
      loggedInUser,
      newDateModel: undefined,
    }) || "";

    const infoLog = {
      orderId: newUniqueId,
      title: OrderInfoName[OrderInfo.full],
      date: now,
      type: OrderActivityType.orderInfo,
      from: "user" as const,
      description: infoDesc,
    };

    await Promise.all([activityRef.add(statusLog), activityRef.add(infoLog)]);

    return { success: true };
  } catch (e) {
    return { success: false, error: "Failed to update order state (admin)" };
  }
}
