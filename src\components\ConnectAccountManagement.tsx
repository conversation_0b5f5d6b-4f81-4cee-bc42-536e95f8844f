"use client";

import React from 'react';
import { 
  ConnectAccountManagement, 
  ConnectNotificationBanner,
  ConnectPayouts,
  ConnectBalances 
} from '@stripe/react-connect-js';
import { useConnectEmbedded } from './ConnectEmbeddedProvider';
import { Loader2, AlertCircle } from 'lucide-react';

interface ConnectAccountManagementWrapperProps {
  accountId: string;
  className?: string;
  showNotificationBanner?: boolean;
}

export const ConnectAccountManagementWrapper: React.FC<ConnectAccountManagementWrapperProps> = ({
  accountId,
  className = "",
  showNotificationBanner = true,
}) => {
  const { stripeConnectInstance, isLoading, error } = useConnectEmbedded();

  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
          <p className="text-gray-600">Loading account management...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="text-center">
          <AlertCircle className="h-8 w-8 text-red-500 mx-auto mb-4" />
          <p className="text-red-600 mb-4">Failed to load account management</p>
          <p className="text-sm text-gray-500">{error}</p>
        </div>
      </div>
    );
  }

  if (!stripeConnectInstance) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="text-center">
          <AlertCircle className="h-8 w-8 text-yellow-500 mx-auto mb-4" />
          <p className="text-gray-600">Connect instance not available</p>
        </div>
      </div>
    );
  }

  return (
    <div className={`connect-account-management-container ${className}`}>
      {/* Notification Banner */}
      {showNotificationBanner && (
        <div className="mb-6">
          <ConnectNotificationBanner />
        </div>
      )}

      <div className="mb-6">
        <h2 className="text-2xl font-bold text-gray-900 mb-2">
          Account Management
        </h2>
        <p className="text-gray-600">
          Manage your account details, business information, and settings.
        </p>
      </div>
      
      <div className="border rounded-lg p-1 bg-white">
        <ConnectAccountManagement />
      </div>
    </div>
  );
};

interface ConnectPayoutsWrapperProps {
  accountId: string;
  className?: string;
}

export const ConnectPayoutsWrapper: React.FC<ConnectPayoutsWrapperProps> = ({
  accountId,
  className = "",
}) => {
  const { stripeConnectInstance, isLoading, error } = useConnectEmbedded();

  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
          <p className="text-gray-600">Loading payouts...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="text-center">
          <AlertCircle className="h-8 w-8 text-red-500 mx-auto mb-4" />
          <p className="text-red-600 mb-4">Failed to load payouts</p>
          <p className="text-sm text-gray-500">{error}</p>
        </div>
      </div>
    );
  }

  if (!stripeConnectInstance) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="text-center">
          <AlertCircle className="h-8 w-8 text-yellow-500 mx-auto mb-4" />
          <p className="text-gray-600">Connect instance not available</p>
        </div>
      </div>
    );
  }

  return (
    <div className={`connect-payouts-container ${className}`}>
      <div className="mb-6">
        <h2 className="text-2xl font-bold text-gray-900 mb-2">
          Payouts & Balance
        </h2>
        <p className="text-gray-600">
          View your balance and manage payout settings.
        </p>
      </div>
      
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Balance */}
        <div className="border rounded-lg p-1 bg-white">
          <h3 className="text-lg font-semibold p-4 border-b">Balance</h3>
          <ConnectBalances />
        </div>

        {/* Payouts */}
        <div className="border rounded-lg p-1 bg-white">
          <h3 className="text-lg font-semibold p-4 border-b">Payouts</h3>
          <ConnectPayouts />
        </div>
      </div>
    </div>
  );
};

export default ConnectAccountManagementWrapper;
