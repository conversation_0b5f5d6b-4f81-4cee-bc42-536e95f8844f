import { getAuth } from "firebase/auth";
import {
  collection,
  doc,
  getFirestore,
  serverTimestamp,
  setDoc,
} from "firebase/firestore";
import { MailServiceManager } from "./MailService";
import { GetUserInfo } from "./usersServices";

export interface Report {
  id?: string;
  post_id?: string;
  user_id?:string;
  reported_user_id: string;

  description: string;
  reason: ReportingReason;
  sub_reason:
    | FraudSubreason
    | IllegalSubreason
    | SensitiveSubreason
    | SpamSubreason;

  created_at?: number;
  updated_at?: number;
}

export enum ReportingReason {
  Fraud = "FRAUD",
  Illegal = "ILLEGAL",
  Sensitive = "SENSITIVE",
  Spam = "SPAM",
}


export enum FraudSubreason {
  Impersonation = "IMPERSONATION",
  Scam = "SCAM",
}
export enum IllegalSubreason {
  AnimalAbuse = "ANIMAL_ABUSE",
  DirectThreat = "DIRECT_THREAT",
  HumanAbuse = "HUMAN_ABUSE",
  IntEllEctualProperty = "INTEllECTUAL_PROPERTY",
  ThreatIndividual = "THREAT_INDIVIDUAL",
  Violence = "VIOLENCE",
}
export enum SensitiveSubreason {
  Nsfw = "NSFW",
  Offensive = "OFFENSIVE",
}
export enum SpamSubreason {
  FakeEngagement = "FAKE_ENGAGEMENT",
  LowSignal = "LOW_SIGNAL",
  ManipulationAlgo = "MANIPULATION_ALGO",
  Misleading = "MISLEADING",
  MisuseHashtags = "MISUSE_HASHTAGS",
  Repetitive = "REPETITIVE",
  SomethingElse = "SOMETHING_ELSE",
  Unrelated = "UNRELATED",
}


 // const resp = await ReportManager.getInstance().reportPost({
    //   description:"asdasdasd",
    //   reason:ReportingReason.Fraud,
    //   sub_reason:FraudSubreason.Scam,
    //   post_id:"uGFDkWyoucCqrikFsxGs",
    //   reported_user_id:"eEeCcJ3In5bWGHbauY4nRHgxuOn2",
    // })


export class ReportManager {
  private REPORT_COLLECTION = "reports";

  static instance: ReportManager | null = null;

  private constructor() {}

  static getInstance() {
    if (!this.instance) {
      this.instance = new ReportManager();
    }
    return this.instance;
  }

  async reportPost(data: Report) {
    try {
      const db = getFirestore();
      const reportsRef = collection(db, this.REPORT_COLLECTION);
      const newReportRef = doc(reportsRef);
      const report_id = newReportRef.id;

    const auth = getAuth();
    const user = auth.currentUser;        
    if(!user?.uid){
      return "failed";
    }

      const resp = await setDoc(doc(reportsRef, report_id), {
        id: report_id,
        ...data,
        updated_at: serverTimestamp(),
        created_at: serverTimestamp(),
      });

      const reportedName:string = (await GetUserInfo(data?.reported_user_id ?? ""))?.profile_name;
      const reportedByName:string = (await GetUserInfo(data?.user_id ?? ""))?.profile_name;
      

      await MailServiceManager.getInstance()?.sendMail({
              toMail: "<EMAIL>",
              type:"post_reported",
              message:{
                reportedUid:data?.reported_user_id,
                reporterUid:data?.user_id ,
                reportedName:reportedName , 
                reporterName:reportedByName,
                reason:data?.description ,
                comment:data?.reason,
                post_id:data?.post_id
              }
            });      
      
      
      
      return "success";
    } catch (error) {
      throw new Error("report_post_failed");
    }
  }

  // pass user_id instead of post_id
  async reportUser(data: Report) {
    try {
      const db = getFirestore();
      const reportsRef = collection(db, this.REPORT_COLLECTION);
      const newReportRef = doc(reportsRef);
      const report_id = newReportRef.id;

      const auth = getAuth();
      const user = auth.currentUser;        
      if(!user?.uid){
        return "failed";
      }

      const resp = await setDoc(doc(reportsRef, report_id), {
        id: report_id,
        ...data,
        updated_at: serverTimestamp(),
        created_at: serverTimestamp(),
      });

      const reportedName:string = (await GetUserInfo(data?.reported_user_id ?? ""))?.profile_name;
      const reportedByName:string = (await GetUserInfo(data?.user_id ?? ""))?.profile_name;
      

      await MailServiceManager.getInstance()?.sendMail({
              toMail: "<EMAIL>",
              type:"profile_reported",
              message:{
                reportedUid:data?.reported_user_id,
                reporterUid:data?.user_id ,
                reportedName:reportedName , 
                reporterName:reportedByName,
                reason:data?.description ,
                comment:data?.reason
              }
            });

      return "success";
    } catch (error) {
      throw new Error("report_post_failed");
    }
  }
  
  
}
