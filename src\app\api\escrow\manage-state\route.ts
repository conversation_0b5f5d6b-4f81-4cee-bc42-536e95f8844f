import { NextRequest, NextResponse } from 'next/server';
import <PERSON><PERSON> from 'stripe';
import { getEscrowTransactionByOrderId, releaseEscrowStage, updateTransaction } from '@/services/transactionService';

if (!process.env.STRIPE_SECRET_KEY) {
  throw new Error("STRIPE_SECRET_KEY is not defined");
}

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY);


// Escrow state management API
export async function POST(request: NextRequest) {
  try {
    const {
      orderId,
      chargeId,
      newState, // 'accept', 'delivered', 'completed'
      metadata = {}
    } = await request.json();

    // Validate required fields
    if (!orderId || !chargeId || !newState) {
      return NextResponse.json({
        error: 'Missing required fields: orderId, chargeId, newState'
      }, { status: 400 });
    }

    // Validate state
    const validStates = ['accept', 'delivered', 'completed'];
    if (!validStates.includes(newState.toLowerCase())) {
      return NextResponse.json({
        error: `Invalid state. Must be one of: ${validStates.join(', ')}`
      }, { status: 400 });
    }

    console.log(`🔄 Processing escrow state change: ${newState} for order ${orderId} with charge ${chargeId}`);

    // Get escrow transaction
    const transactionResult = await getEscrowTransactionByOrderId(orderId);
    if (!transactionResult.success || !transactionResult.transaction) {
      return NextResponse.json({
        error: 'Escrow transaction not found for this order'
      }, { status: 404 });
    }

    const transaction = transactionResult.transaction;
    const state = newState.toLowerCase();

    // Get current charge status from Stripe
    let charge;
    try {
      charge = await stripe.charges.retrieve(chargeId);
      console.log(`💳 Charge status: ${charge.status}, captured: ${charge.captured}, amount_capturable: ${charge.amount_captured}`);
    } catch (error) {
      console.error('Error retrieving charge:', error);
      return NextResponse.json({
        error: 'Invalid charge ID or charge not found'
      }, { status: 400 });
    }

    let result = { success: false, message: '', stripeData: null };

    switch (state) {
      case 'accept':
        // ACCEPT: Capture the first 10% of payment
        console.log('🎯 Processing ACCEPT state - Capturing 10%');

        if (charge.captured) {
          return NextResponse.json({
            error: 'Payment has already been captured. Cannot process accept state.'
          }, { status: 400 });
        }

        // Calculate 10% for accept stage
        const acceptAmount = Math.round(charge.amount * 0.10);

        try {
          // Capture 10% of the payment
          const captureResult = await stripe.charges.capture(chargeId, {
            amount: acceptAmount,
            metadata: {
              escrowStage: 'accept',
              orderId,
              ...(metadata?.orderUniqueId ? { orderUniqueId: (metadata as any).orderUniqueId } : {}),
              transactionId: transaction.id,
              ...metadata
            }
          });

          console.log(`✅ Captured ${acceptAmount} (10%) for accept stage`);

          // Create transfer to seller (84% of captured amount)
          const transferAmount = Math.round(acceptAmount * 0.84);

          const transfer = await stripe.transfers.create({
            amount: transferAmount,
            currency: charge.currency,
            destination: transaction.sellerStripeAccountId,
            source_transaction: chargeId,
            metadata: {
              escrowStage: 'accept',
              orderId,
              ...(metadata?.orderUniqueId ? { orderUniqueId: (metadata as any).orderUniqueId } : {}),
              transactionId: transaction.id,
              capturedAmount: acceptAmount,
              ...metadata
            }
          });

          console.log(`💸 Transferred ${transferAmount} to seller for accept stage`);

          // Update transaction in Firebase
          await releaseEscrowStage(transaction.id, 'accept', {
            stripeChargeId: chargeId,
            capturedAmount: acceptAmount,
            transferredAmount: transferAmount,
            transferId: transfer.id,
            updatedAt: new Date()
          });

          result = {
            success: true,
            message: 'Accept stage processed successfully',
            stripeData: {
              charge: captureResult,
              transfer: transfer,
              capturedAmount: acceptAmount,
              transferredAmount: transferAmount,
              remainingCapturable: charge.amount - acceptAmount
            }
          };

        } catch (error) {
          console.error('Error processing accept stage:', error);
          return NextResponse.json({
            error: `Failed to process accept stage: ${error instanceof Error ? error.message : 'Unknown error'}`
          }, { status: 500 });
        }
        break;

      case 'delivered':
        // DELIVERED: Capture another 10% (total 20%)
        console.log('🚚 Processing DELIVERED state - Capturing additional 10%');

        // Get updated charge info
        const updatedCharge = await stripe.charges.retrieve(chargeId);
        const remainingAmount = updatedCharge.amount - updatedCharge.amount_captured;
        const deliveredAmount = Math.round(updatedCharge.amount * 0.10);

        if (remainingAmount < deliveredAmount) {
          return NextResponse.json({
            error: 'Insufficient capturable amount for delivered stage'
          }, { status: 400 });
        }

        try {
          // Capture additional 10%
          const captureResult = await stripe.charges.capture(chargeId, {
            amount: deliveredAmount,
            metadata: {
              escrowStage: 'delivered',
              orderId,
              ...(metadata?.orderUniqueId ? { orderUniqueId: (metadata as any).orderUniqueId } : {}),
              transactionId: transaction.id,
              ...metadata
            }
          });

          // Transfer 84% of captured amount to seller
          const transferAmount = Math.round(deliveredAmount * 0.84);

          const transfer = await stripe.transfers.create({
            amount: transferAmount,
            currency: updatedCharge.currency,
            destination: transaction.sellerStripeAccountId,
            source_transaction: chargeId,
            metadata: {
              escrowStage: 'delivered',
              orderId,
              ...(metadata?.orderUniqueId ? { orderUniqueId: (metadata as any).orderUniqueId } : {}),
              transactionId: transaction.id,
              capturedAmount: deliveredAmount,
              ...metadata
            }
          });

          // Update transaction
          await releaseEscrowStage(transaction.id, 'delivered', {
            stripeChargeId: chargeId,
            capturedAmount: deliveredAmount,
            transferredAmount: transferAmount,
            transferId: transfer.id,
            updatedAt: new Date()
          });

          result = {
            success: true,
            message: 'Delivered stage processed successfully',
            stripeData: {
              charge: captureResult,
              transfer: transfer,
              capturedAmount: deliveredAmount,
              transferredAmount: transferAmount,
              totalCaptured: updatedCharge.amount_captured + deliveredAmount,
              remainingCapturable: updatedCharge.amount - updatedCharge.amount_captured - deliveredAmount
            }
          };

        } catch (error) {
          console.error('Error processing delivered stage:', error);
          return NextResponse.json({
            error: `Failed to process delivered stage: ${error instanceof Error ? error.message : 'Unknown error'}`
          }, { status: 500 });
        }
        break;

      case 'completed':
        // COMPLETED: Capture remaining 80%
        console.log('🎉 Processing COMPLETED state - Capturing remaining 80%');

        const finalCharge = await stripe.charges.retrieve(chargeId);
        const finalRemainingAmount = finalCharge.amount - finalCharge.amount_captured;

        if (finalRemainingAmount <= 0) {
          return NextResponse.json({
            error: 'No remaining amount to capture for completed stage'
          }, { status: 400 });
        }

        try {
          // Capture remaining amount (should be 80%)
          const captureResult = await stripe.charges.capture(chargeId, {
            amount: finalRemainingAmount,
            metadata: {
              escrowStage: 'completed',
              orderId,
              ...(metadata?.orderUniqueId ? { orderUniqueId: (metadata as any).orderUniqueId } : {}),
              transactionId: transaction.id,
              ...metadata
            }
          });

          // Transfer 84% of captured amount to seller
          const transferAmount = Math.round(finalRemainingAmount * 0.84);

          const transfer = await stripe.transfers.create({
            amount: transferAmount,
            currency: finalCharge.currency,
            destination: transaction.sellerStripeAccountId,
            source_transaction: chargeId,
            metadata: {
              escrowStage: 'completed',
              orderId,
              ...(metadata?.orderUniqueId ? { orderUniqueId: (metadata as any).orderUniqueId } : {}),
              transactionId: transaction.id,
              capturedAmount: finalRemainingAmount,
              finalStage: true,
              ...metadata
            }
          });

          // Update transaction to completed
          await releaseEscrowStage(transaction.id, 'completed', {
            stripeChargeId: chargeId,
            capturedAmount: finalRemainingAmount,
            transferredAmount: transferAmount,
            transferId: transfer.id,
            status: 'completed',
            updatedAt: new Date()
          });

          result = {
            success: true,
            message: 'Escrow completed successfully - all funds released',
            stripeData: {
              charge: captureResult,
              transfer: transfer,
              capturedAmount: finalRemainingAmount,
              transferredAmount: transferAmount,
              totalCaptured: finalCharge.amount,
              escrowCompleted: true
            }
          };

        } catch (error) {
          console.error('Error processing completed stage:', error);
          return NextResponse.json({
            error: `Failed to process completed stage: ${error instanceof Error ? error.message : 'Unknown error'}`
          }, { status: 500 });
        }
        break;
    }

    return NextResponse.json(result);

  } catch (error) {
    console.error('Escrow state management error:', error);
    return NextResponse.json({
      error: 'Failed to process escrow state change',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
