# Apple Pay Troubleshooting Guide

## Quick Checklist

### ✅ Requirements Met?
- [ ] Domain registered with <PERSON><PERSON>
- [ ] HTTPS enabled
- [ ] Using Safari browser (recommended)
- [ ] Apple device with Touch ID/Face ID
- [ ] Cards added to Apple Wallet
- [ ] Correct Stripe keys configured

## Common Issues & Solutions

### 1. Apple Pay Button Not Showing

**Symptoms:**
- Express Checkout Element shows no payment methods
- Console shows "No payment methods available"

**Solutions:**

#### A. Register Your Domain
```bash
# Using the script
node scripts/register-domain.js your-domain.com

# Or via API endpoint
curl -X POST http://localhost:3000/api/stripe/register-domain \
  -H "Content-Type: application/json" \
  -d '{"domain_name": "your-domain.com"}'
```

#### B. Check Domain Registration
```bash
# List registered domains
node scripts/register-domain.js list

# Or visit the test page
http://localhost:3000/apple-pay-test
```

#### C. Verify HTTPS
- Apple Pay requires HTTPS in production
- For local development, use ngrok: `ngrok http 3000`
- Register the ngrok domain: `node scripts/register-domain.js abc123.ngrok.io`

### 2. "Invalid paymentMethodTypes" Error

**Error Message:**
```
IntegrationError: Invalid value for elements(): paymentMethodTypes.5 should be one of the following strings: ... You specified: google_pay.
```

**Solution:**
- Apple Pay and Google Pay are handled by `ExpressCheckoutElement`, not `paymentMethodTypes`
- Remove `apple_pay` and `google_pay` from `paymentMethodTypes` array
- Use only: `['card', 'link', 'paypal', 'amazon_pay', 'klarna']`

### 3. Browser Compatibility Issues

**Supported Browsers:**
- ✅ Safari (Mac/iOS) - Best support
- ⚠️ Chrome (limited support)
- ❌ Firefox (no support)

**Device Requirements:**
- Mac with Touch ID or Apple Watch
- iPhone/iPad with Face ID or Touch ID
- Cards must be added to Apple Wallet

### 4. Development vs Production

**Development (Test Mode):**
- Use test Stripe keys
- Register localhost or ngrok domains
- Can use real cards (won't be charged)

**Production (Live Mode):**
- Use live Stripe keys
- Register production domains
- Real payments will be processed

## Debugging Steps

### 1. Run Diagnostics
Visit `/apple-pay-test` page and click "Run Diagnostics"

### 2. Check Console Logs
Look for these log messages:
```javascript
🍎 APPLE PAY DEBUGGING
🔍 Apple Pay Session available: true/false
🔍 Apple Pay can make payments: true/false
🚀 Express Checkout ready!
💳 Available payment methods: {...}
```

### 3. Verify Stripe Configuration
```javascript
// Check in browser console
console.log('Stripe Key:', process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY);
```

### 4. Test Apple Pay Availability
```javascript
// Run in Safari console
if (window.ApplePaySession) {
  console.log('Apple Pay supported:', ApplePaySession.canMakePayments());
} else {
  console.log('Apple Pay not supported');
}
```

## API Endpoints

### Register Domain
```
POST /api/stripe/register-domain
{
  "domain_name": "example.com"
}
```

### List Domains
```
GET /api/stripe/list-domains
```

## Environment Variables

Make sure these are set:
```env
STRIPE_SECRET_KEY=sk_test_...
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_...
```

## Testing Checklist

1. **Domain Registration**
   - [ ] Current domain is registered with Stripe
   - [ ] Domain shows as "enabled" in Stripe Dashboard

2. **Browser Setup**
   - [ ] Using Safari browser
   - [ ] Not in private/incognito mode
   - [ ] Apple Pay enabled in Safari settings

3. **Device Setup**
   - [ ] Touch ID or Face ID enabled
   - [ ] At least one card in Apple Wallet
   - [ ] Device supports Apple Pay

4. **Code Configuration**
   - [ ] Using ExpressCheckoutElement
   - [ ] Not including apple_pay in paymentMethodTypes
   - [ ] Correct Stripe keys configured

5. **Network**
   - [ ] HTTPS enabled
   - [ ] No network restrictions blocking Stripe

## Still Having Issues?

1. Check the [Stripe test page](https://docs.stripe.com/testing/wallets)
2. Compare with working demo integrations
3. Verify all requirements in [Stripe Apple Pay docs](https://docs.stripe.com/apple-pay?platform=web)
4. Check Stripe Dashboard for any account restrictions

## Useful Links

- [Stripe Apple Pay Documentation](https://docs.stripe.com/apple-pay?platform=web)
- [Apple Pay Web Requirements](https://developer.apple.com/apple-pay/web/)
- [Stripe Test Cards](https://docs.stripe.com/testing#cards)
- [Express Checkout Element](https://docs.stripe.com/elements/express-checkout-element)
