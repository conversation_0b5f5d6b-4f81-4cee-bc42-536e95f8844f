"use client";
import { Badge } from "@/components/ui/badge";
import { useEffect, useState } from "react";
import { Check, CheckSquare, Info, Square, ChevronLeft, Loader } from "react-feather";
import ViewService from "./ViewService";
import { Label } from "@/components/ui/label";

import { Textarea } from "@/components/ui/textarea";
import { Button } from "@/components/ui/button";
import { formatDuration, getServiceById } from "@/services/serviceService";
import { getCurrencySymbol, initializeCurrency } from "@/services/currencyService";
import ServiceDescription from "@/globalComponents/formatText";
import { Modal, ModalBody, ModalContent } from "@heroui/react";
import { CreateOrder_V2 } from "@/services/ordersServices";
import useAuth from "@/hook";
import { useRouter } from "next/navigation";

const OtherServiceDetails = ({ id, onSelectServiceDetails, otherUserID }: any) => {
  const [isTrue, setIsTrue] = useState(false);
  const [isCheck, setIsCheck] = useState(Array(0).fill(false));
  const [selectedIndex, setSelectedIndex] = useState<number | null>(null); // Track selected icon index
  const [custamizationId, setCustamizationId] = useState("");
  const [isOpen, setIsOpen] = useState(false);
  const [isOpenLeave, setIsOpenLeave] = useState(false);
  const [currencySymbol, setCurrencySymbol] = useState("£"); // Default currency symbol
  const [totalCost, setTotalCost] = useState(0);
  const [totalDuration, setTotalDuration] = useState<number>(0);
  const [isLoadingService, setIsLoadingService] = useState(true);
  const [isLoadingCustomizations, setIsLoadingCustomizations] = useState(true);
  const auth = useAuth();
  const router = useRouter();

  const handleSelectService = ({ id }: any) => {
    setIsTrue(false);
    setSelectedIndex(null);
  };

  const handleIsCheck = (id: number) => {
    const newState = [...isCheck];
    newState[id] = !newState[id];
    setIsCheck(newState);

    const basePrice = parseFloat(services?.price) || 0;
    const baseDuration = parseFloat(services?.duration) || 0;

    let additionalCost = 0;
    let additionalDuration = 0;

    if (services?.customizations_array) {
      services.customizations_array.forEach((item: any, idx: number) => {
        if (newState[idx]) {
          additionalCost += parseFloat(item.price) || 0;

          const itemDuration = parseFloat(item.duration);
          if (!isNaN(itemDuration)) {
            additionalDuration += itemDuration;
          }
        }
      });
    }

    const finalCost = (basePrice + additionalCost) / (1 - 0.16);
    setTotalCost(finalCost);

    const totalTime = baseDuration + additionalDuration;
    setTotalDuration(totalTime);
  };

  const handleInfoClick = (index: number) => {
    setSelectedIndex(index === selectedIndex ? null : index); // Deselect if clicked again
    setIsTrue(true);
  };

  const [services, setServices]: any = useState();

  useEffect(() => {
    if (services?.duration) {
      setTotalDuration(parseFloat(services.duration) || 0);
    }
  }, [services]);

  useEffect(() => {
    const fetchAllServices = async () => {
      // Set loading states
      setIsLoadingService(true);
      setIsLoadingCustomizations(true);

      try {
        const response = await getServiceById(id);
        if (response.success) {
          setServices(response.service);

          // Initialize checkboxes
          if (response.service?.customizations_array) {
            setIsCheck(Array(response.service.customizations_array.length).fill(false));
          }

          // Set initial total
          try {
            // @ts-ignore
            const initialPrice = parseFloat(response.service?.price) || 0;
            setTotalCost(initialPrice / (1 - 0.16));

            // Set initial duration
            const initialDuration = 0;
            setTotalDuration(initialDuration);
          } catch (e) {
            setTotalCost(0);
            setTotalDuration(0);
          }

          // Set currency symbol based on service data
          const servicesCurrency = initializeCurrency();
          setCurrencySymbol(getCurrencySymbol(servicesCurrency));
        }
      } catch (error) {
        console.error("Error fetching service:", error);
      } finally {
        // Add a small delay to ensure loading state is visible
        setTimeout(() => {
          setIsLoadingService(false);
          setIsLoadingCustomizations(false);
        }, 500);
      }
    };

    fetchAllServices();
  }, [id]);

  // add to basket
  const addToBasket = async () => {
    try {
      // Get selected customizations IDs
      const selectedCustomizations =
        services?.customizations_array
          ?.filter((_: any, index: number) => isCheck[index])
          .map((item: any) => item.id) || [];

      // Get comment from textarea
      const comment =
        (document.querySelector('textarea[placeholder="Add comment"]') as HTMLTextAreaElement)
          ?.value || "";

      const resp = await CreateOrder_V2({
        orderData: {
          profileId: otherUserID,
          serviceId: services?.id,
          status: "BASKET",
          userProfileId: auth?.userId,
          comment: comment,
          selectedCustomizations: selectedCustomizations,
        },
      });
    } catch (error) {
      console.error("Error adding to basket:", error);
      alert(`Error adding to basket: ${error instanceof Error ? error.message : "Unknown error"}`);
    }
  };

  return (
    <>
      <div
        className={
          isTrue
            ? "grid grid-cols-2 max-md:grid-cols-1 bg-white gap-4"
            : "grid grid-cols-1 bg-white"
        }
      >
        <div className={isTrue ? "max-md:hidden bg-white" : "bg-white"}>
          {/* header */}
          <div className=" w-full bg-white sticky top-[6.2rem] pt-3">
            <div className="row gap-3 justify-between">
              <div
                onClick={() => !isTrue && onSelectServiceDetails()}
                className=" cursor-pointer row gap-1"
                aria-disabled={true}
              >
                <ChevronLeft />
                <p>Back</p>
              </div>
              <p className="text-titleLabel text-lg font-bold">Service Details</p>
              <p className=" opacity-0"></p>
            </div>
          </div>
          <div
            className={
              isTrue
                ? "flex flex-row w-full overflow-y-scroll gap-3 hide-scroll-custom  bg-white h-[calc(100vh-300px)] pt-4 pb-16"
                : "flex flex-row w-full overflow-y-scroll gap-3 hide-scroll-custom bg-white h-[calc(100vh-300px)] pt-4"
            }
          >
            <div className="px-2 w-full">
              {isLoadingService ? (
                <div className="flex flex-col items-center justify-center h-[60vh]">
                  <div className="bg-blue-50 rounded-full p-4 mb-4">
                    <Loader size={48} className="text-primary animate-spin" />
                  </div>
                  <h2 className="text-xl font-bold mb-2">Loading Service Details...</h2>
                  <p className="text-gray-500 text-center">
                    Please wait while we load your service information.
                  </p>
                </div>
              ) : (
                <>
                  <p className="my-3 text-primary text-lg font-bold max-md:text-base">
                    {services?.title}
                  </p>
                  <div className="row justify-between">
                    <p className="text-titleLabel">
                      {formatDuration(services?.duration)} Day
                      {Number(formatDuration(services?.duration)) !== 1 ? "s" : ""}
                    </p>
                    <p className="text-titleLabel">
                      {currencySymbol}
                      {services?.price ? (services?.price / (1 - 0.16)).toFixed(2) : "0.00"}
                    </p>
                  </div>
                  <div className="text-subtitle mt-3 break-words whitespace-pre-wrap">
                    <ServiceDescription description={services?.description} />
                  </div>

                  {services?.customizations_array.length !== 0 && (
                    <>
                      <p className="my-3 text-primary text-lg font-semibold">Customization</p>

                      {isLoadingCustomizations ? (
                        <div className="flex flex-col justify-center items-center py-8">
                          <div className="bg-blue-50 rounded-full p-3 mb-2">
                            <Loader size={24} className="text-primary animate-spin" />
                          </div>
                          <p className="text-gray-500 text-sm">Loading customizations...</p>
                        </div>
                      ) : services?.customizations_array &&
                        services?.customizations_array.length > 0 ? (
                        <div>
                          <div className="grid grid-cols-2">
                            <div>
                              <p className="text-primary font-semibold">Option</p>
                            </div>
                            <div className="justify-end row">
                              <div className="row gap-4">
                                <p className="text-primary font-semibold w-14 text-center">Time</p>
                                <p className="text-primary font-semibold  w-14 text-center">Cost</p>
                                <p className=" opacity-0  w-8 text-center">hi</p>
                              </div>
                            </div>
                          </div>

                          <div>
                            {services?.customizations_array.map((item: any, indexs: any) => (
                              <div className="grid grid-cols-2 mt-2" key={indexs}>
                                <div className="row gap-3">
                                  {isCheck[indexs] ? (
                                    <CheckSquare
                                      className="text-primary w-[18px] min-w-[18px] min-h-[18px] max-w-[18px] max-h-[18px] cursor-pointer"
                                      size={18}
                                      onClick={() => handleIsCheck(indexs)}
                                    />
                                  ) : (
                                    <Square
                                      className="text-subtitle w-[18px] min-w-[18px] min-h-[18px] max-w-[18px] max-h-[18px] cursor-pointer"
                                      // size={18}
                                      onClick={() => handleIsCheck(indexs)}
                                    />
                                  )}
                                  <p
                                    className={` cursor-pointer text-left text-sm ${
                                      isCheck[indexs]
                                        ? "font-bold text-primary"
                                        : "font-normal text-subtitle"
                                    }`}
                                    onClick={() => handleIsCheck(indexs)}
                                  >
                                    {item?.title}
                                  </p>
                                </div>
                                <div className="justify-end row">
                                  <div className="row gap-4">
                                    <p
                                      className={` w-14 text-left text-sm ${
                                        isCheck[indexs]
                                          ? "font-bold text-primary"
                                          : "font-normal text-subtitle"
                                      }`}
                                    >
                                      +
                                      {formatDuration(item?.duration, {
                                        dayLabel: "d ",
                                        hourLabel: "h",
                                      })}
                                    </p>
                                    <p
                                      className={` w-14 text-right text-sm text-nowrap ${
                                        isCheck[indexs]
                                          ? "font-bold text-primary"
                                          : "font-normal text-subtitle"
                                      }`}
                                    >
                                      +{currencySymbol}
                                      {item?.price ? (item?.price / (1 - 0.16)).toFixed(2) : "0.00"}
                                    </p>
                                    <Info
                                      className={`cursor-pointer w-8 text-center ${
                                        selectedIndex === indexs
                                          ? "fill-subtitle text-white border-borderColor"
                                          : "text-borderColor"
                                      }`}
                                      size={21}
                                      onClick={() => {
                                        !isTrue && handleInfoClick(indexs),
                                          setCustamizationId(item?.id);
                                      }}
                                    />
                                  </div>
                                </div>
                              </div>
                            ))}
                          </div>
                        </div>
                      ) : (
                        <p className="text-gray-500 text-center py-4">
                          No customizations available for this service.
                        </p>
                      )}
                    </>
                  )}
                </>
              )}

              {!isLoadingService && (
                <>
                  <div className="grid w-full items-center gap-1.5 mt-3">
                    <Label className="text-base font-[600] text-titleLabel">Сomment</Label>
                    <Textarea
                      placeholder="Add comment"
                      className="resize-none h-40 outline-none text-lg text-primary"
                    />
                  </div>

                  <div>
                    <p className="items-end justify-end flex my-3 text-subtitle2">
                      Approximate time{" "}
                      <span className="font-bold ml-2 text-black">
                        {formatDuration(totalDuration, {
                          dayLabel: "day",
                          hourLabel: "hour",
                          handlePlural: true,
                        })}
                      </span>
                    </p>
                    <p className="items-end justify-end flex tetx-subtitle2">
                      Cost{" "}
                      <span className="font-bold ml-2 text-black">
                        {currencySymbol}
                        {`${(Math.floor(totalCost * 100) / 100).toFixed(2)}`}
                      </span>
                    </p>
                  </div>

                  <div className="flex gap-2 mt-3">
                    <Info className="text-borderColor w-[18px] min-w-[18px] min-h-[18px] max-w-[18px] max-h-[18px] mt-1" />
                    <p className="text-primary text-sm">
                      If for some reason you are not ready to make an order, just add it to the
                      basket and you will be able to get back to it later.{" "}
                    </p>
                  </div>

                  <div className="w-full mt-5">
                    <Badge
                      className="btn-xs py-4 border-primary btn w-full"
                      variant="outline"
                      onClick={() => {
                        setIsOpen(true), addToBasket();
                      }}
                    >
                      Add to Basket
                    </Badge>
                  </div>
                </>
              )}
            </div>
          </div>
        </div>

        {/* Edit Service */}
        {isTrue && (
          <ViewService
            onSelectService={handleSelectService}
            id={id}
            custamizationId={custamizationId}
            currencySymbol={currencySymbol}
          />
        )}
      </div>

      {/* Add to Basket Modal */}
      <div>
        <Modal
          isDismissable={false}
          isOpen={isOpen}
          placement="auto"
          onOpenChange={setIsOpen}
          hideCloseButton={true}
        >
          <ModalContent className="modal-content">
            {(onClose) => (
              <>
                <ModalBody>
                  <div className="row justify-center">
                    <Check size={100} className="text-primary -mt-6" />
                  </div>
                  <p className="text-center text-black text-lg">
                    Service "{services?.title || "Selected service"}" was added to your basket.
                  </p>
                  <div className="px-12">
                    <Badge
                      className="btn-xs py-4  border-2 border-primary py-[18px] text-base btn w-full"
                      variant="outline"
                      onClick={() => setIsOpen(false)}
                    >
                      Continue browsing
                    </Badge>
                    <Badge
                      className="btn-xs py-4 mt-3 border-2 border-primary py-[18px] text-base btn w-full"
                      variant="outline"
                      onClick={() => {
                        setIsOpen(false);
                        router.push(`${window.location.pathname}?sidebar=basket`, {
                          scroll: false,
                        });
                        window.dispatchEvent(new Event("openSidebar"));
                      }}
                    >
                      Go to Basket
                    </Badge>
                  </div>
                </ModalBody>
              </>
            )}
          </ModalContent>
        </Modal>
      </div>

      {/* leave Modal */}
      <div>
        <Modal
          isDismissable={false}
          isOpen={isOpenLeave}
          placement="auto"
          onOpenChange={setIsOpenLeave}
          hideCloseButton={true}
        >
          <ModalContent className="modal-content">
            {(onClose) => (
              <>
                <ModalBody>
                  <p className="text-center text-black text-lg">
                    Are you sure you want to leave? All customized service settings will be lost.
                  </p>
                  <div className="px-12">
                    <Badge
                      className="btn-xs py-4  border-2 border-primary py-[18px] text-base btn w-full"
                      variant="outline"
                      onClick={() => setIsOpenLeave(false)}
                    >
                      Yes, leave
                    </Badge>
                    <Badge
                      className="btn-xs py-4 mt-3 border-2 border-primary py-[18px] text-base btn w-full"
                      variant="outline"
                      onClick={() => setIsOpenLeave(false)}
                    >
                      No, cancel
                    </Badge>
                  </div>
                </ModalBody>
              </>
            )}
          </ModalContent>
        </Modal>
      </div>
    </>
  );
};

export default OtherServiceDetails;
