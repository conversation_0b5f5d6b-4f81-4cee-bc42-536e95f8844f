import React, { useEffect, useState, useRef, useCallback } from "react";
import { FilterSearchManager, ProfileSearchBy } from "@/services/filtersServices";
import GlobalProfileCard from "@/globalComponents/globalProfileCard";
import GlobalProfileCardLens from "@/globalComponents/globalProfileCardLens";
import EmptyState from "@/components/EmptyState";
import { themes } from "../../../theme";
import { getLensProfilesById } from "@/services/lensService";
import { AccountsOrderBy, PageSize, useAccountsQuery } from "@/graphql/test/generated";
import { sortProfiles } from "@/lib/helper";
import { getLocation } from "@/lib/utils";

const Profiles = ({
  searchFor,
  searchBy,
  search,
  SEARCH_FOR,
  SEARCH_BY_MAP,
  onClose,
  filters,
}: any) => {
  const [profiles, setProfiles] = useState<any[]>([]); // loaded profiles
  const [loading, setLoading] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const [nextStartAfterAddedAt, setNextStartAfterAddedAt] = useState<any>(undefined);
  const [error, setError] = useState<string | null>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const isFetchingRef = useRef(false);
  const PAGE_SIZE = 50;

  // Fetch profiles (initial or paginated)
  const fetchProfiles = useCallback(
    async (isInitial = false) => {
      let filterBy;
      if (searchBy === "Profile name") {
        filterBy = ProfileSearchBy.PROFILE_NAME;
      } else if (searchBy === "Hashtag") {
        filterBy = ProfileSearchBy.HASHTAGS;
      } else if (searchBy === "Location") {
        filterBy = ProfileSearchBy.LOCATION;
      } else {
        setProfiles([]);
        setHasMore(false);
        setNextStartAfterAddedAt(undefined);
        return;
      }

      // Prevent multiple simultaneous requests
      if (isFetchingRef.current || loading) return;

      isFetchingRef.current = true;
      setLoading(true);
      setError(null);
      try {
        const resp = await FilterSearchManager.getInstance().GetProfileByFilters({
          payload: {
            filterBy,
            searchTerm: search,
            limit: PAGE_SIZE,
            startAfterAddedAt: isInitial ? undefined : nextStartAfterAddedAt,
            filters,
          },
        });

        if (isInitial) {
          setProfiles(resp.profiles || []);
        } else {
          setProfiles((prev) => [...prev, ...(resp.profiles || [])]);
        }
        setNextStartAfterAddedAt(resp.nextStartAfterAddedAt);
        setHasMore(resp.hasMore);
      } catch (error: any) {
        setError(error && error.message ? error.message : "Error fetching profiles");
      } finally {
        isFetchingRef.current = false;
        setLoading(false);
      }
    },
    [search, searchBy, filters, loading, nextStartAfterAddedAt]
  );

  // Initial fetch or when search/searchBy changes
  useEffect(() => {
    setProfiles([]);
    setNextStartAfterAddedAt(undefined);
    setHasMore(true);
    isFetchingRef.current = false; // Reset fetching state
    fetchProfiles(true);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [search, searchBy, filters]);

  // Scroll handler for backend pagination
  const handleScroll = useCallback(() => {
    const container = containerRef.current;
    if (!container || loading || !hasMore) return;

    const { scrollTop, scrollHeight, clientHeight } = container;
    // Trigger load when user is within 100px of the bottom
    if (scrollHeight - scrollTop - clientHeight < 100) {
      fetchProfiles(false);
    }
  }, [loading, hasMore, fetchProfiles]);

  useEffect(() => {
    const container = containerRef.current;
    if (!container) return;
    container.addEventListener("scroll", handleScroll);
    return () => container.removeEventListener("scroll", handleScroll);
  }, [handleScroll]);

  const hasProfiles = Array.isArray(profiles) && profiles.length > 0;

  // fetch lens profile
  // const address: string = "0xb492b704b59f7657F677D617411EA86669b59D71";
  const { data: profileSearch } = useAccountsQuery(
    {
      request: {
        //  addresses:[address],
        // legacyProfileIds:["joanakawaharalino"]
        filter: {
          searchBy: {
            localNameQuery: search,
          },
        },
        orderBy: AccountsOrderBy.BestMatch,
        pageSize: PageSize.Fifty,
        cursor: null,
      },
    },
    {
      refetchOnWindowFocus: false,
      // enabled: !!address,
    }
  );
  console.log({ profileSearch });

  const mergedProfiles = sortProfiles(
    [
      ...(profiles || []).map((item) => ({
        ...item,
        profile_name: item.profile_name || "Profile Name*",
      })),
      ...(profileSearch?.accounts?.items || []).map((profile) => ({
        id: profile.username?.localName,
        profile_name: profile.metadata?.name || "Profile Name*",
        avatar: profile?.metadata?.picture || "",
        location: getLocation(profile.metadata?.attributes) || profile.username?.localName,
        isFollow: profile?.operations?.isFollowedByMe,
        lensProfile: true, // Mark lens profiles
      })),
    ],
    "profile_name"
  );

  return (
    <div ref={containerRef} className="overflow-y-auto  h-[calc(100vh-200px)] min-h-0">
      {hasProfiles || mergedProfiles.length > 0 ? (
        <div className="grid grid-cols-4 max-md:grid-cols-1 max-lg:grid-cols-2 gap-3 mt-4">
          {mergedProfiles
            .filter((item: any) => {
              // For lens profiles, always show them
              if (item.lensProfile) return true;
              // For regular profiles, check categories
              return Array.isArray(item.categories) && item.categories.length > 0;
            })
            .map((post: any) => {
              // For lens profiles, use GlobalProfileCardLens
              if (post.lensProfile) {
                return (
                  <div key={post.id} onClick={onClose} className="border-b-2">
                    <GlobalProfileCardLens
                      themeProperties={Object.values(themes)[0]} // Use first theme as default
                      isFollow={post.isFollow}
                      location={post.location}
                      profile_name={post.profile_name}
                      avatar={post.avatar}
                      id={post.id}
                    />
                  </div>
                );
              }

              // For regular profiles, match with themes
              const matchedTheme = Object.entries(themes).find(
                ([_key, theme]: [string, any]) =>
                  (Array.isArray(post.categories) && post.categories.length > 0
                    ? post.categories[0] === "Storytelling"
                      ? "Literature"
                      : post.categories[0]
                    : null) === theme.title
              );
              if (!matchedTheme) return null;

              return (
                <div key={post.id} onClick={onClose} className="border-b-2">
                  <GlobalProfileCard
                    location={post.location}
                    profile_name={post.profile_name}
                    avatar={post.avatar}
                    id={post.id}
                    themeProperties={matchedTheme[1]}
                  />
                </div>
              );
            })}
        </div>
      ) : loading ? (
        <div className="flex justify-center items-center h-full py-8">
          <span className="loader">Loading...</span>
        </div>
      ) : (
        <div className="flex justify-center items-center h-full">
          <EmptyState
            type="profiles"
            title="No profiles found"
            message="Try adjusting your search or filter to find profiles."
          />
        </div>
      )}
      {/* Show loading spinner at bottom if loading more */}
      {hasProfiles && loading && (
        <div className="flex justify-center items-center py-4">
          <span className="loader">Loading...</span>
        </div>
      )}
      {/* Show 'No more data found' if all data loaded */}
      {hasProfiles && !hasMore && !loading && (
        <div className="flex justify-center items-center py-4 text-gray-500">
          No more data found
        </div>
      )}
      {/* Show error if any */}
      {error && <div className="text-red-500 text-center py-2">{error}</div>}
    </div>
  );
};

export default Profiles;
