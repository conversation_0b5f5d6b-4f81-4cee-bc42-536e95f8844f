"use client";

import React from 'react';
import { ConnectPayments, ConnectPaymentDetails } from '@stripe/react-connect-js';
import { useConnectEmbedded } from './ConnectEmbeddedProvider';
import { Loader2, AlertCircle } from 'lucide-react';

interface ConnectPaymentsWrapperProps {
  accountId: string;
  className?: string;
  onPaymentSelect?: (paymentId: string) => void;
}

export const ConnectPaymentsWrapper: React.FC<ConnectPaymentsWrapperProps> = ({
  accountId,
  className = "",
  onPaymentSelect,
}) => {
  const { stripeConnectInstance, isLoading, error } = useConnectEmbedded();

  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
          <p className="text-gray-600">Loading payments...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="text-center">
          <AlertCircle className="h-8 w-8 text-red-500 mx-auto mb-4" />
          <p className="text-red-600 mb-4">Failed to load payments</p>
          <p className="text-sm text-gray-500">{error}</p>
        </div>
      </div>
    );
  }

  if (!stripeConnectInstance) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="text-center">
          <AlertCircle className="h-8 w-8 text-yellow-500 mx-auto mb-4" />
          <p className="text-gray-600">Connect instance not available</p>
        </div>
      </div>
    );
  }

  return (
    <div className={`connect-payments-container ${className}`}>
      <div className="mb-6">
        <h2 className="text-2xl font-bold text-gray-900 mb-2">
          Payment Management
        </h2>
        <p className="text-gray-600">
          View and manage your payments, refunds, and disputes.
        </p>
      </div>
      
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Payments List */}
        <div className="border rounded-lg p-1 bg-white">
          <h3 className="text-lg font-semibold p-4 border-b">Payments</h3>
          <ConnectPayments />
        </div>

        {/* Payment Details */}
        {/* Payment Details: Use a separate route or modal when needed */}
      </div>
    </div>
  );
};

interface ConnectPaymentDetailsWrapperProps {
  accountId: string;
  paymentId: string;
  className?: string;
  onRefund?: (refundId: string) => void;
  onDispute?: (disputeId: string) => void;
}

export const ConnectPaymentDetailsWrapper: React.FC<ConnectPaymentDetailsWrapperProps> = ({
  accountId,
  paymentId,
  className = "",
  onRefund,
  onDispute,
}) => {
  const { stripeConnectInstance, isLoading, error } = useConnectEmbedded();

  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
          <p className="text-gray-600">Loading payment details...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="text-center">
          <AlertCircle className="h-8 w-8 text-red-500 mx-auto mb-4" />
          <p className="text-red-600 mb-4">Failed to load payment details</p>
          <p className="text-sm text-gray-500">{error}</p>
        </div>
      </div>
    );
  }

  if (!stripeConnectInstance) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="text-center">
          <AlertCircle className="h-8 w-8 text-yellow-500 mx-auto mb-4" />
          <p className="text-gray-600">Connect instance not available</p>
        </div>
      </div>
    );
  }

  return (
    <div className={`connect-payment-details-container ${className}`}>
      <div className="mb-6">
        <h2 className="text-2xl font-bold text-gray-900 mb-2">
          Payment Details
        </h2>
        <p className="text-gray-600">
          Detailed view of payment #{paymentId}
        </p>
      </div>
      
      <div className="border rounded-lg p-1 bg-white">
        <ConnectPaymentDetails payment={paymentId} onClose={() => {}} />
      </div>
    </div>
  );
};

export default ConnectPaymentsWrapper;
