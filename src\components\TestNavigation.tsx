'use client';

import React, { useState } from 'react';
import Link from 'next/link';

export default function TestNavigation() {
  const [isOpen, setIsOpen] = useState(false);

  // Only show in development
  if (process.env.NODE_ENV !== 'development') {
    return null;
  }

  return (
    <div className="fixed bottom-4 right-4 z-50">
      {/* Toggle Button */}
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="bg-blue-600 hover:bg-blue-700 text-white p-3 rounded-full shadow-lg transition-colors"
        title="Payment Testing Tools"
      >
        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4" />
        </svg>
      </button>

      {/* Menu */}
      {isOpen && (
        <div className="absolute bottom-16 right-0 bg-white rounded-lg shadow-xl border p-4 min-w-64">
          <h3 className="font-semibold text-gray-900 mb-3">🧪 Payment Testing</h3>
          
          <div className="space-y-2">
            <Link
              href="/payment-test"
              className="block p-3 rounded border hover:bg-gray-50 transition-colors"
              onClick={() => setIsOpen(false)}
            >
              <div className="font-medium text-blue-600">Payment Forms Test</div>
              <div className="text-xs text-gray-500">Test all payment forms with fixes</div>
            </Link>
            
            <Link
              href="/apple-pay-test"
              className="block p-3 rounded border hover:bg-gray-50 transition-colors"
              onClick={() => setIsOpen(false)}
            >
              <div className="font-medium text-green-600">Apple Pay Diagnostics</div>
              <div className="text-xs text-gray-500">Debug Apple Pay setup</div>
            </Link>

            <Link
              href="/test-capture-methods"
              className="block p-3 rounded border hover:bg-gray-50 transition-colors"
              onClick={() => setIsOpen(false)}
            >
              <div className="font-medium text-purple-600">Capture Method Test</div>
              <div className="text-xs text-gray-500">Test automatic vs manual capture</div>
            </Link>
          </div>

          <div className="mt-4 pt-3 border-t">
            <div className="text-xs text-gray-500">
              <div className="font-medium mb-1">Quick Commands:</div>
              <div className="font-mono bg-gray-100 p-2 rounded text-xs">
                node scripts/test-api.js<br/>
                node scripts/register-domain.js localhost:3000
              </div>
            </div>
          </div>

          <button
            onClick={() => setIsOpen(false)}
            className="absolute top-2 right-2 text-gray-400 hover:text-gray-600"
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
      )}
    </div>
  );
}
