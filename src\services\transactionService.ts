import { initFirebase } from "../../firebaseConfig";
import { collection, doc, setDoc, getDoc, updateDoc, query, where, orderBy, limit, getDocs, Timestamp } from "firebase/firestore";

export interface Transaction {
  id: string;
  userId: string;
  userEmail: string;
  stripeSessionId?: string;
  stripePaymentIntentId?: string;
  stripeCustomerId?: string;
  amount: number;
  currency: string;
  status: 'pending' | 'completed' | 'failed' | 'cancelled';
  productName: string;
  productDescription?: string;
  metadata?: Record<string, any>;
  createdAt: Date;
  updatedAt: Date;
  completedAt?: Date;

  // Escrow-specific fields
  isEscrow?: boolean;
  sellerId?: string;
  sellerEmail?: string;
  sellerStripeAccountId?: string;
  orderId?: string;

  // Payment breakdown
  subtotal?: number;
  transactionFee?: number; // 4% fee
  platformCommission?: number; // 16% commission
  sellerAmount?: number; // Amount that goes to seller (84% of subtotal)

  // Escrow stages
  escrowStages?: EscrowStage[];
  currentStage?: 'pending' | 'accepted' | 'delivered' | 'completed';

  // Payment authorization and capture tracking
  paymentAuthorized?: boolean;
  paymentAuthorizedAt?: Date;
  paymentCaptured?: boolean;
  paymentCapturedAt?: Date;
  awaitingCapture?: boolean;
}

export interface EscrowStage {
  stage: 'accept' | 'delivered' | 'completed';
  percentage: number; // 10, 10, 80
  amount: number;
  status: 'pending' | 'released' | 'failed';
  releasedAt?: Date;
  stripeTransferId?: string;
  orderStatus?: string; // The order status that triggers this release
  capturedAt?: Date; // When payment was captured for this stage
  requiresCapture?: boolean; // Whether this stage requires payment capture
}

export const createTransaction = async (transactionData: Omit<Transaction, 'id' | 'createdAt' | 'updatedAt'>): Promise<{ success: boolean; transactionId?: string; error?: string }> => {
  try {
    const { db } = await initFirebase();
    const transactionsCollection = collection(db, "transactions");

    // Generate a unique transaction ID
    const transactionRef = doc(transactionsCollection);
    const transactionId = transactionRef.id;

    const transaction: Transaction = {
      ...transactionData,
      id: transactionId,
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    await setDoc(transactionRef, {
      ...transaction,
      createdAt: Timestamp.fromDate(transaction.createdAt),
      updatedAt: Timestamp.fromDate(transaction.updatedAt),
      completedAt: transaction.completedAt ? Timestamp.fromDate(transaction.completedAt) : null,
      escrowStages: transaction.escrowStages || null,
    });

    return { success: true, transactionId };
  } catch (error) {
    console.error("Error creating transaction:", error);
    return { success: false, error: "Failed to create transaction" };
  }
};

export const createEscrowTransaction = async (escrowData: {
  userId: string;
  userEmail: string;
  sellerId: string;
  sellerEmail?: string; // optional, will auto-resolve from usersServices if missing
  sellerStripeAccountId?: string; // optional, will auto-resolve from usersServices if missing
  orderId: string;
  amount: number; // Total order amount ($104 in your example)
  currency: string;
  productName: string;
  productDescription?: string;
  stripeSessionId?: string;
  stripeCustomerId?: string;
  metadata?: Record<string, any>;
}): Promise<{ success: boolean; transactionId?: string; error?: string }> => {
  try {
    console.log('Creating escrow transaction with data:', {
      userId: escrowData.userId,
      sellerId: escrowData.sellerId,
      orderId: escrowData.orderId,
      amount: escrowData.amount,
      currency: escrowData.currency
    });

    // Validate required fields
    if (!escrowData.userId || !escrowData.sellerId || !escrowData.orderId || !escrowData.amount) {
      return { success: false, error: 'Missing required escrow data fields' };
    }

    // Resolve seller Stripe account ID using only usersServices.GetUserStripeId
    try {
      if (!escrowData.sellerStripeAccountId) {
        const { GetUserStripeId } = await import("@/services/usersServices");
        const resolvedStripeId = await GetUserStripeId(escrowData.sellerId);
        escrowData = {
          ...escrowData,
          sellerStripeAccountId: resolvedStripeId || escrowData.sellerStripeAccountId,
        };
      }
    } catch (e) {
      console.warn("createEscrowTransaction: failed to resolve seller Stripe ID via GetUserStripeId", e);
    }

    // Calculate payment breakdown based on your example:
    // Note: escrowData.amount is already in smallest currency unit (cents/pence)
    // For £12.34, amount = 1234 pence
    // For $104.00, amount = 10400 cents

    // Convert to major currency unit for calculations
    const totalInMajorUnit = escrowData.amount / 100; // £12.34 or $104.00
    const subtotalInMajorUnit = totalInMajorUnit / 1.04; // Remove 4% to get subtotal
    const transactionFeeInMajorUnit = totalInMajorUnit - subtotalInMajorUnit; // 4% fee
    const platformCommissionInMajorUnit = subtotalInMajorUnit * 0.16; // 16% commission
    const sellerAmountInMajorUnit = subtotalInMajorUnit - platformCommissionInMajorUnit; // 84% goes to seller

    // Convert back to smallest currency unit for storage
    const subtotal = Math.round(subtotalInMajorUnit * 100);
    const transactionFee = Math.round(transactionFeeInMajorUnit * 100);
    const platformCommission = Math.round(platformCommissionInMajorUnit * 100);
    const sellerAmount = Math.round(sellerAmountInMajorUnit * 100);

    console.log('Payment breakdown:', {
      total: escrowData.amount,
      totalInMajorUnit,
      subtotal,
      subtotalInMajorUnit,
      transactionFee,
      transactionFeeInMajorUnit,
      platformCommission,
      platformCommissionInMajorUnit,
      sellerAmount,
      sellerAmountInMajorUnit,
      currency: escrowData.currency
    });

    // Create escrow stages: 10%, 10%, 80% of seller amount
    // Calculate stage amounts in smallest currency unit (cents/pence)
    const stage1Amount = Math.round(sellerAmount * 0.10); // 10%
    const stage2Amount = Math.round(sellerAmount * 0.10); // 10%
    const stage3Amount = sellerAmount - stage1Amount - stage2Amount; // Remaining 80% (ensures total adds up)

    const escrowStages: EscrowStage[] = [
      {
        stage: 'accept',
        percentage: 10,
        amount: stage1Amount,
        status: 'pending',
        orderStatus: 'accept',
        requiresCapture: true // First stage requires payment capture
      },
      {
        stage: 'delivered',
        percentage: 10,
        amount: stage2Amount,
        status: 'pending',
        orderStatus: 'delivered',
        requiresCapture: false // Subsequent stages use already captured funds
      },
      {
        stage: 'completed',
        percentage: 80,
        amount: stage3Amount,
        status: 'pending',
        orderStatus: 'completed',
        requiresCapture: false // Subsequent stages use already captured funds
      }
    ];

    console.log('Escrow stages:', escrowStages.map(stage => ({
      stage: stage.stage,
      percentage: stage.percentage,
      amount: stage.amount,
      amountInMajorUnit: stage.amount / 100
    })));

    const transactionData: Omit<Transaction, 'id' | 'createdAt' | 'updatedAt'> = {
      ...escrowData,
      isEscrow: true,
      status: 'pending',
      currentStage: 'pending',
      subtotal,
      transactionFee,
      platformCommission,
      sellerAmount,
      escrowStages,
      // Initialize authorization tracking
      paymentAuthorized: false,
      paymentCaptured: false,
      awaitingCapture: false
    };

    console.log('Creating transaction with escrow data...');
    const result = await createTransaction(transactionData);

    if (result.success) {
      console.log('Escrow transaction created successfully:', result.transactionId);
    } else {
      console.error('Failed to create escrow transaction:', result.error);
    }

    return result;
  } catch (error) {
    console.error("Error creating escrow transaction:", error);
    const errorMessage = error instanceof Error ? error.message : "Unknown error";
    return { success: false, error: `Failed to create escrow transaction: ${errorMessage}` };
  }
};

// Helper function to remove undefined values from objects
const cleanUndefinedValues = (obj: any): any => {
  const cleaned: any = {};
  for (const [key, value] of Object.entries(obj)) {
    if (value !== undefined) {
      if (value && typeof value === 'object' && !Array.isArray(value) && !(value instanceof Date) && !(value instanceof Timestamp)) {
        cleaned[key] = cleanUndefinedValues(value);
      } else {
        cleaned[key] = value;
      }
    }
  }
  return cleaned;
};

export const updateTransaction = async (
  transactionId: string,
  updates: Partial<Omit<Transaction, 'id' | 'createdAt'>>
): Promise<{ success: boolean; error?: string }> => {
  try {
    const { db } = await initFirebase();
    const transactionRef = doc(db, "transactions", transactionId);

    const updateData: any = {
      ...updates,
      updatedAt: Timestamp.fromDate(new Date()),
    };

    // Convert Date objects to Timestamps
    if (updates.completedAt) {
      updateData.completedAt = Timestamp.fromDate(updates.completedAt);
    }

    // Clean undefined values before sending to Firestore
    const cleanedUpdateData = cleanUndefinedValues(updateData);

    await updateDoc(transactionRef, cleanedUpdateData);

    return { success: true };
  } catch (error) {
    console.error("Error updating transaction:", error);
    return { success: false, error: "Failed to update transaction" };
  }
};

export const getTransaction = async (transactionId: string): Promise<{ success: boolean; transaction?: Transaction; error?: string }> => {
  try {
    const { db } = await initFirebase();
    const transactionRef = doc(db, "transactions", transactionId);
    const transactionSnap = await getDoc(transactionRef);
    
    if (transactionSnap.exists()) {
      const data = transactionSnap.data();
      const transaction: Transaction = {
        ...data,
        createdAt: data.createdAt.toDate(),
        updatedAt: data.updatedAt.toDate(),
        completedAt: data.completedAt ? data.completedAt.toDate() : undefined,
      } as Transaction;
      
      return { success: true, transaction };
    } else {
      return { success: false, error: "Transaction not found" };
    }
  } catch (error) {
    console.error("Error getting transaction:", error);
    return { success: false, error: "Failed to get transaction" };
  }
};

export const getTransactionByStripeSessionId = async (sessionId: string): Promise<{ success: boolean; transaction?: Transaction; error?: string }> => {
  try {
    const { db } = await initFirebase();
    const transactionsCollection = collection(db, "transactions");
    const q = query(
      transactionsCollection,
      where("stripeSessionId", "==", sessionId),
      limit(1)
    );
    
    const querySnapshot = await getDocs(q);
    
    if (!querySnapshot.empty) {
      const doc = querySnapshot.docs[0];
      const data = doc.data();
      const transaction: Transaction = {
        ...data,
        createdAt: data.createdAt.toDate(),
        updatedAt: data.updatedAt.toDate(),
        completedAt: data.completedAt ? data.completedAt.toDate() : undefined,
      } as Transaction;
      
      return { success: true, transaction };
    } else {
      return { success: false, error: "Transaction not found" };
    }
  } catch (error) {
    console.error("Error getting transaction by session ID:", error);
    return { success: false, error: "Failed to get transaction" };
  }
};

export const getUserTransactions = async (
  userId: string,
  limitCount: number = 10
): Promise<{ success: boolean; transactions?: Transaction[]; error?: string }> => {
  try {
    const { db } = await initFirebase();
    const transactionsCollection = collection(db, "transactions");
    const q = query(
      transactionsCollection,
      where("userId", "==", userId),
      orderBy("createdAt", "desc"),
      limit(limitCount)
    );

    const querySnapshot = await getDocs(q);
    const transactions: Transaction[] = [];

    querySnapshot.forEach((doc) => {
      const data = doc.data();
      transactions.push({
        ...data,
        createdAt: data.createdAt.toDate(),
        updatedAt: data.updatedAt.toDate(),
        completedAt: data.completedAt ? data.completedAt.toDate() : undefined,
      } as Transaction);
    });

    return { success: true, transactions };
  } catch (error) {
    console.error("Error getting user transactions:", error);
    return { success: false, error: "Failed to get transactions" };
  }
};

export const findDuplicateTransactions = async (
  userId: string
): Promise<{ success: boolean; duplicates?: Transaction[]; error?: string }> => {
  try {
    const { db } = await initFirebase();
    const transactionsCollection = collection(db, "transactions");
    const q = query(
      transactionsCollection,
      where("userId", "==", userId),
      orderBy("createdAt", "desc")
    );

    const querySnapshot = await getDocs(q);
    const transactions: Transaction[] = [];
    const sessionIdMap = new Map<string, Transaction[]>();

    querySnapshot.forEach((doc) => {
      const data = doc.data();
      const transaction = {
        ...data,
        createdAt: data.createdAt.toDate(),
        updatedAt: data.updatedAt.toDate(),
        completedAt: data.completedAt ? data.completedAt.toDate() : undefined,
      } as Transaction;

      transactions.push(transaction);

      // Group by session ID to find duplicates
      if (transaction.stripeSessionId) {
        if (!sessionIdMap.has(transaction.stripeSessionId)) {
          sessionIdMap.set(transaction.stripeSessionId, []);
        }
        sessionIdMap.get(transaction.stripeSessionId)!.push(transaction);
      }
    });

    // Find duplicates (sessions with more than one transaction)
    const duplicates: Transaction[] = [];
    sessionIdMap.forEach((transactionGroup) => {
      if (transactionGroup.length > 1) {
        // Keep the first one (oldest), mark others as duplicates
        duplicates.push(...transactionGroup.slice(1));
      }
    });

    return { success: true, duplicates };
  } catch (error) {
    console.error("Error finding duplicate transactions:", error);
    return { success: false, error: "Failed to find duplicates" };
  }
};

export const releaseEscrowStage = async (
  transactionId: string,
  stage: 'accept' | 'delivered' | 'completed',
  stripeTransferId?: string
): Promise<{ success: boolean; error?: string }> => {
  try {
    console.log('🔄 Releasing escrow stage:', { transactionId, stage, stripeTransferId });

    const { db } = await initFirebase();
    const transactionRef = doc(db, "transactions", transactionId);
    const transactionSnap = await getDoc(transactionRef);

    if (!transactionSnap.exists()) {
      return { success: false, error: "Transaction not found" };
    }

    const transaction = transactionSnap.data() as Transaction;

    if (!transaction.isEscrow || !transaction.escrowStages) {
      return { success: false, error: "Not an escrow transaction" };
    }

    console.log('📊 Current escrow stages:', transaction.escrowStages.map(s => ({
      stage: s.stage,
      status: s.status,
      amount: s.amount,
      stripeTransferId: s.stripeTransferId
    })));

    // Find and update the specific stage
    const updatedStages = transaction.escrowStages.map(escrowStage => {
      if (escrowStage.stage === stage) {
        const updatedStage: any = {
          ...escrowStage,
          status: 'released' as const,
          releasedAt: new Date()
        };

        // Only add stripeTransferId if it's defined
        if (stripeTransferId) {
          updatedStage.stripeTransferId = stripeTransferId;
        }

        // Only add capturedAt if it's defined or if this stage requires capture
        if (escrowStage.requiresCapture) {
          updatedStage.capturedAt = new Date();
        } else if (escrowStage.capturedAt) {
          updatedStage.capturedAt = escrowStage.capturedAt;
        }

        return updatedStage;
      }
      return escrowStage;
    });

    // Update current stage
    let currentStage = transaction.currentStage;
    if (stage === 'accept') currentStage = 'accepted';
    else if (stage === 'delivered') currentStage = 'delivered';
    else if (stage === 'completed') currentStage = 'completed';

    const updateData = {
      escrowStages: updatedStages,
      currentStage,
      updatedAt: Timestamp.fromDate(new Date())
    };

    console.log('📝 Update data before cleaning:', JSON.stringify(updateData, null, 2));

    // Clean undefined values before sending to Firestore
    const cleanedUpdateData = cleanUndefinedValues(updateData);

    console.log('✨ Cleaned update data:', JSON.stringify(cleanedUpdateData, null, 2));

    await updateDoc(transactionRef, cleanedUpdateData);

    console.log('✅ Escrow stage released successfully:', { stage, currentStage });

    return { success: true };
  } catch (error) {
    console.error("Error releasing escrow stage:", error);
    return { success: false, error: "Failed to release escrow stage" };
  }
};

export const getEscrowTransactionByOrderId = async (orderId: string): Promise<{ success: boolean; transaction?: Transaction; error?: string }> => {
  try {
    const { db } = await initFirebase();
    const transactionsCollection = collection(db, "transactions");
    const q = query(
      transactionsCollection,
      where("orderId", "==", orderId),
      where("isEscrow", "==", true)
    );

    const querySnapshot = await getDocs(q);

    if (querySnapshot.empty) {
      return { success: false, error: "Escrow transaction not found for this order" };
    }

    const docSnap = querySnapshot.docs[0];
    const data = docSnap.data();
    let transaction: Transaction = {
      ...data,
      createdAt: data.createdAt.toDate(),
      updatedAt: data.updatedAt.toDate(),
      completedAt: data.completedAt ? data.completedAt.toDate() : undefined,
      escrowStages: data.escrowStages?.map((stage: any) => ({
        ...stage,
        releasedAt: stage.releasedAt ? stage.releasedAt.toDate() : undefined
      }))
    } as Transaction;

    // Ensure sellerStripeAccountId is populated via usersServices if missing
    try {
      if (!transaction.sellerStripeAccountId && transaction.sellerId) {
        const { GetUserStripeId } = await import("@/services/usersServices");
        const stripeId = await GetUserStripeId(transaction.sellerId);
        if (stripeId) {
          transaction = { ...transaction, sellerStripeAccountId: stripeId };
        }
      }
    } catch (e) {
      console.warn("Unable to enrich transaction with seller Stripe ID:", e);
    }

    return { success: true, transaction };
  } catch (error) {
    console.error("Error getting escrow transaction by order ID:", error);
    return { success: false, error: "Failed to get escrow transaction" };
  }
};

export const updatePaymentAuthorizationStatus = async (
  transactionId: string,
  authorized: boolean,
  captured?: boolean
): Promise<{ success: boolean; error?: string }> => {
  try {
    const { db } = await initFirebase();
    const transactionRef = doc(db, "transactions", transactionId);

    const updateData: any = {
      paymentAuthorized: authorized,
      updatedAt: Timestamp.fromDate(new Date())
    };

    if (authorized) {
      updateData.paymentAuthorizedAt = Timestamp.fromDate(new Date());
      updateData.awaitingCapture = !captured;
    }

    if (captured !== undefined) {
      updateData.paymentCaptured = captured;
      if (captured) {
        updateData.paymentCapturedAt = Timestamp.fromDate(new Date());
        updateData.awaitingCapture = false;
      }
    }

    await updateDoc(transactionRef, updateData);

    return { success: true };
  } catch (error) {
    console.error("Error updating payment authorization status:", error);
    return { success: false, error: "Failed to update payment authorization status" };
  }
};
