import {
  collection,
  addDoc,
  getDocs,
  doc,
  updateDoc,
  Timestamp,
  query,
  where,
  serverTimestamp,
  setDoc,
  orderBy,
  onSnapshot,
  limit,
  or,
  getDoc,
} from "firebase/firestore";
import { BASE_URL, GATE_URL, getIdToken } from "@/lib/utils";

// Interface
interface SendMailProps {
  from?: string; // e.g. "AMUZN <<EMAIL>>"
  toMail: string; // recipient email
  subject?: string; // email subject
  html?: string; // HTML body
  text?: string; // optional plain text body
  message: Record<string, any>; // extra metadata (optional)
  type: EmailType;
}
type EmailType =
  | "profile_deleted"
  | "profile_reported"
  | "order_update"
  | "invoice_request"
  | "stripe_connect"
  | "post_reported"
  | "verify_email"
  | "reset_password"
  | "order_contact"; // for contact b/w buyer & seller 

interface EmailPayload {
  type: EmailType;
  to: string; // recipient
  data?: Record<string, any>;
}
///// ///// ///// ///// ///// ///// ///// ///// ///// ///// ///// ///// ///// ///// /////

export class MailServiceManager {
  private MAIL_COLLECTION = "mail";

  static instance: MailServiceManager | null = null;

  private constructor() {}

  static getInstance() {
    if (!this.instance) {
      this.instance = new MailServiceManager();
    }
    return this.instance;
  }

  async test() {
    const response = await fetch(GATE_URL, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        apple: "apple",
      }),
    });
    if (response.status !== 200) {
      const errorText = await response.text();
      console.log({ errorText });
      return; // don't update Firestore if cancel failed
    }
    const result = await response.json();
    return result;
  }

  async sendMail({ from, toMail, subject, html, text, message = {}, type }: SendMailProps) {
    try {
      const { from, to, subject, html } = this.buildEmail({
        to: toMail,
        type,
        data: message,
      });

      const idToken = await getIdToken();

      const response = await fetch(GATE_URL, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${idToken}`,
        },
        body: JSON.stringify({
          type: "sendMail",
          mail_type: type,
          message,
          payload: {
            to,
          },
        }),
      });
      if (response.status !== 200) {
        const errorText = await response.text();
        console.log({ errorText });
        return; // don't update Firestore if cancel failed
      }
      const result = await response.json();
      return result;
    } catch (error) {
      console.error("sendMail failed:", error);
      throw new Error("sendMail failed");
    }
  }
    async sendResetPasswordMail({ from, toMail, subject, html, text, message = {}, type }: SendMailProps) {
    try {
      const { from, to, subject, html } = this.buildEmail({
        to: toMail,
        type,
        data: message,
      });


      const response = await fetch(GATE_URL, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          type: "sendResetPasswordMail",
          mail_type: type,
          message,
          payload: {
            to,
          },
        }),
      });
      if (response.status !== 200) {
        const errorText = await response.text();
        console.log({ errorText });
        return; // don't update Firestore if cancel failed
      }
      const result = await response.json();
      return result;
    } catch (error) {
      console.error("sendMail failed:", error);
      throw new Error("sendMail failed");
    }
  }

  buildEmail({ type, to, data = {} }: EmailPayload) {
    const from = "<EMAIL>";

    let subject = "";
    let html = "";

    switch (type) {
      case "profile_deleted":
        subject = "Profile deleted";
        html = `
        <div>
          <p>Profile deleted: ${data.profileId}</p>
          <p>Reason: ${data.reason}</p>
             <br/>
        <p>Thanks,<br/>Your AMUZN Team</p>
        </div>`;
        break;

      case "profile_reported":
        subject = "Profile reported";
        html = `
        <div>
          <p>Profile reported: ${data.reportedName} (user UID: ${data.reportedUid})</p>
          <p>Profile reported by: ${data.reporterName} (user UID: ${data.reporterUid})</p>
          <p>Report reason: ${data.reason}</p>
          <p>Comment: ${data.comment}</p>
             <br/>
        <p>Thanks,<br/>Your AMUZN Team</p>
        </div>`;
        break;
      case "post_reported":
        subject = "Post reported";
        html = `
        <div>
          <p>Post owner: ${data.reportedName} (user UID: ${data.reportedUid})</p>
          <p>Post reported by: ${data.reporterName} (user UID: ${data.reporterUid})</p>
          <p>Post : ${data?.post_id}</p>
          <p>Report reason: ${data.reason}</p>
          <p>Comment: ${data.comment}</p>
         <br/>
        <p>Thanks,<br/>Your AMUZN Team</p>
        </div>`;
        break;

      case "order_update":
        subject = `AMUZN Order status update - ${data.orderId}: ${data.status}`;
        html = `
         <p>
          Hi ${data?.name} ,
        </p>
        <br/>
        <br/>
        <pre style="font-family: inherit; white-space: pre-wrap;">${data.message}</pre>
         <br/>
         <br/>
        <p>Thanks,<br/>Your AMUZN Team</p>
        `;
        break;

      case "invoice_request":
        subject = `Invoice - customer tax information request - ${data.orderId}`;
        html = `
        <div>
          <p>Hello,</p>
          <p>Thank you for your request for an invoice.</p>
          <p>Please provide your tax details below as required in order to issue you an invoice.</p>
          <ul>
            <li>Legal name</li>
            <li>Street address</li>
            <li>Town/City</li>
            <li>Postcode (e.g. EC1A1BB)</li>
            <li>Country</li>
            <li>VAT number (e.g. *********)</li>
          </ul>
          <p>An invoice will be created and emailed to you including your details provided above.</p>
          <p>If any questions please contact us at <a href="mailto:<EMAIL>"><EMAIL></a></p>
          <p>Your AMUZN Team</p>
        </div>`;
        break;

      // ask louis from where to trigger this
      case "stripe_connect":
        subject = "Connect Stripe to sell on AMUZN";
        html = `
        <div>
          <p>Don't forget to connect Stripe to your Amuzn profile so you can start selling services.</p>
          <p>Go to <b>Connect Account</b> in Amuzn app left menu and follow the instructions.</p>
        </div>`;
        break;

      case "verify_email":
        subject = "Verify your email address - AMUZN";
        html = `
    <div style="font-family: Arial, sans-serif; line-height:1.6; color:#333;">
          <p>Hello,</p>
      <p>Thanks for signing up with AMUZN! Please verify your email address by clicking the button below:</p>
      <p style="text-align:center; margin: 24px 0;">
        <a href="${data.verificationUrl}" 
           style="background:#000; color:#fff; padding:12px 20px; text-decoration:none; border-radius:6px; font-weight:bold;">
          Verify Email
        </a>
      </p>
      <br/>
      <p>If you didn’t request this, you can safely ignore this email.</p>
      <p>Thanks,<br/>Your AMUZN Team</p>
    </div>
  `;
        break;

    case "reset_password":
        subject = "Reset your password - AMUZN";
        html = `
    <div style="font-family: Arial, sans-serif; line-height:1.6; color:#333;">
          <p>Hello,</p>
      <p> Please reset your password  by clicking the button below:</p>
      <p style="text-align:center; margin: 24px 0;">
        <a href="${data.resetPasswordUrl}" 
           style="background:#000; color:#fff; padding:12px 20px; text-decoration:none; border-radius:6px; font-weight:bold;">
          Reset Password
        </a>
      </p>
      <br/>
      <p>If you didn’t request this, you can safely ignore this email.</p>
      <p>Thanks,<br/>Your AMUZN Team</p>
    </div>
  `;
        break;        

    case "order_contact":
  subject = `Query regarding Order #${data.orderId} - AMUZN`;
  html = `
    <div style="font-family: Arial, sans-serif; line-height:1.6; color:#333;">
      <p>Hello,</p>

      <p> I am a ${data?.name} and have a query regarding <strong>Order #${data.orderId}</strong>.</p>

      <p>Could we connect and have a quick chat about this?</p>

      <br/>
      <p>Looking forward to your response.</p>
      
      <p>Thank you,<br/>
      <p>Thanks,<br/>Your AMUZN Team</p>
    </div>
  `;
        break;        
    }
      

    return { from, to, subject, html };
  }
}
