"use client";

import React, { useState, useEffect } from 'react';
import { loadStripe } from '@stripe/stripe-js';
import {
  Elements,
  PaymentElement,
  useStripe,
  useElements
} from '@stripe/react-stripe-js';
import { ConnectEmbeddedProvider } from './ConnectEmbeddedProvider';
import { useCurrentUser } from '@/hooks/useCurrentUser';
import { Button } from './ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from './ui/card';
import { Loader2, AlertCircle, CheckCircle, CreditCard } from 'lucide-react';

const stripePromise = loadStripe(process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY!);

interface ConnectEnhancedPaymentFormProps {
  amount: number;
  currency?: string;
  productName?: string;
  userId?: string;
  sellerId?: string;
  sellerAccountId?: string;
  orderId?: string;
  isEscrow?: boolean;
  onSuccess?: (paymentIntent: any) => void;
  onError?: (error: string) => void;
  className?: string;
}

interface PaymentFormProps {
  clientSecret: string;
  amount: number;
  currency: string;
  productName: string;
  sellerId?: string;
  sellerAccountId?: string;
  onSuccess?: (paymentIntent: any) => void;
  onError?: (error: string) => void;
}

function PaymentForm({
  clientSecret,
  amount,
  currency,
  productName,
  sellerId,
  sellerAccountId,
  onSuccess,
  onError
}: PaymentFormProps) {
  const stripe = useStripe();
  const elements = useElements();
  const [isLoading, setIsLoading] = useState(false);
  const [message, setMessage] = useState<string>('');

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!stripe || !elements) {
      return;
    }

    setIsLoading(true);
    setMessage('');

    // Submit the form to validate all fields
    const { error: submitError } = await elements.submit();
    if (submitError) {
      setMessage(submitError.message || "Please check your payment details");
      onError?.(submitError.message || "Please check your payment details");
      setIsLoading(false);
      return;
    }

    try {
      // Confirm payment with Connect account if specified
      const confirmParams: any = {
        elements,
        confirmParams: {
          return_url: `${window.location.origin}/payment-success`,
        },
        redirect: "if_required"
      };

      // If this is a Connect payment, specify the connected account
      if (sellerAccountId) {
        confirmParams.stripeAccount = sellerAccountId;
      }

      const { error, paymentIntent } = await stripe.confirmPayment(confirmParams);

      if (error) {
        console.error('Payment confirmation error:', error);
        setMessage(error.message || "Payment failed");
        onError?.(error.message || "Payment failed");
      } else if (paymentIntent) {
        console.log('Payment succeeded:', paymentIntent);
        setMessage("Payment succeeded!");
        onSuccess?.(paymentIntent);
      }
    } catch (err) {
      console.error('Payment error:', err);
      setMessage("An unexpected error occurred");
      onError?.("An unexpected error occurred");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div className="space-y-4">
        <div className="p-4 bg-blue-50 rounded-lg">
          <h3 className="font-semibold text-blue-900 mb-2">Payment Details</h3>
          <div className="text-sm text-blue-800 space-y-1">
            <p><strong>Amount:</strong> ${(amount / 100).toFixed(2)} {currency.toUpperCase()}</p>
            <p><strong>Product:</strong> {productName}</p>
            {sellerId && <p><strong>Seller:</strong> {sellerId}</p>}
          </div>
        </div>

        <div className="border rounded-lg p-4">
          <PaymentElement 
            options={{
              layout: "tabs",
              paymentMethodOrder: ['card', 'apple_pay', 'google_pay']
            }}
          />
        </div>
      </div>

      <Button
        type="submit"
        disabled={!stripe || !elements || isLoading}
        className="w-full"
        size="lg"
      >
        {isLoading ? (
          <>
            <Loader2 className="h-4 w-4 mr-2 animate-spin" />
            Processing...
          </>
        ) : (
          <>
            <CreditCard className="h-4 w-4 mr-2" />
            Pay ${(amount / 100).toFixed(2)}
          </>
        )}
      </Button>

      {message && (
        <div className={`p-4 rounded-lg ${
          message.includes("succeeded") 
            ? "bg-green-50 text-green-700 border border-green-200" 
            : "bg-red-50 text-red-700 border border-red-200"
        }`}>
          <div className="flex items-center">
            {message.includes("succeeded") ? (
              <CheckCircle className="h-5 w-5 mr-2" />
            ) : (
              <AlertCircle className="h-5 w-5 mr-2" />
            )}
            {message}
          </div>
        </div>
      )}
    </form>
  );
}

export const ConnectEnhancedPaymentForm: React.FC<ConnectEnhancedPaymentFormProps> = ({
  amount,
  currency = "usd",
  productName = "Product",
  userId,
  sellerId,
  sellerAccountId,
  orderId,
  isEscrow = false,
  onSuccess,
  onError,
  className = "",
}) => {
  const { user } = useCurrentUser();
  const [clientSecret, setClientSecret] = useState("");
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const createPaymentIntent = async () => {
      if (!amount || amount <= 0) {
        setError("Invalid amount");
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        setError(null);

        console.log('🔄 Creating Connect payment intent...', { 
          amount, 
          currency, 
          isEscrow,
          sellerAccountId 
        });

        const endpoint = isEscrow ? "/api/escrow/create-payment-intent" : "/api/create-payment-intent";

        const response = await fetch(endpoint, {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({
            amount,
            currency,
            productName,
            userId: userId || user?.uid,
            sellerId,
            sellerAccountId, // For Connect payments
            orderId,
            isEscrow,
            userEmail: user?.email,
            userName: user?.displayName,
            // Connect-specific parameters
            applicationFeeAmount: sellerAccountId ? Math.round(amount * 0.05) : undefined, // 5% platform fee
            transferData: sellerAccountId ? {
              destination: sellerAccountId,
            } : undefined,
          }),
        });

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();

        if (!data.success) {
          throw new Error(data.error || 'Failed to create payment intent');
        }

        console.log('✅ Connect payment intent created:', data);
        setClientSecret(data.client_secret || data.clientSecret);

      } catch (err) {
        console.error('Error creating payment intent:', err);
        const errorMessage = err instanceof Error ? err.message : 'Failed to create payment intent';
        setError(errorMessage);
        onError?.(errorMessage);
      } finally {
        setLoading(false);
      }
    };

    createPaymentIntent();
  }, [amount, currency, productName, userId, sellerId, sellerAccountId, orderId, isEscrow, user]);

  if (loading) {
    return (
      <Card className={className}>
        <CardContent className="flex items-center justify-center p-8">
          <div className="text-center">
            <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
            <p className="text-gray-600">Setting up payment...</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className={className}>
        <CardContent className="flex items-center justify-center p-8">
          <div className="text-center">
            <AlertCircle className="h-8 w-8 text-red-500 mx-auto mb-4" />
            <p className="text-red-600 mb-4">Payment setup failed</p>
            <p className="text-sm text-gray-500">{error}</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!clientSecret) {
    return (
      <Card className={className}>
        <CardContent className="flex items-center justify-center p-8">
          <div className="text-center">
            <AlertCircle className="h-8 w-8 text-yellow-500 mx-auto mb-4" />
            <p className="text-gray-600">Payment not ready</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  const stripeOptions = {
    clientSecret,
    appearance: {
      theme: 'stripe' as const,
      variables: {
        colorPrimary: '#635BFF',
        colorBackground: '#ffffff',
        colorText: '#30313d',
        colorDanger: '#df1b41',
        fontFamily: 'system-ui, sans-serif',
        spacingUnit: '4px',
        borderRadius: '8px',
      },
    },
  };

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center">
          <CreditCard className="h-5 w-5 mr-2" />
          {sellerAccountId ? 'Connect Payment' : 'Payment'}
        </CardTitle>
        <CardDescription>
          {sellerAccountId 
            ? 'Secure payment powered by Stripe Connect' 
            : 'Secure payment powered by Stripe'
          }
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Elements stripe={stripePromise} options={stripeOptions}>
          <PaymentForm
            clientSecret={clientSecret}
            amount={amount}
            currency={currency}
            productName={productName}
            sellerId={sellerId}
            sellerAccountId={sellerAccountId}
            onSuccess={onSuccess}
            onError={onError}
          />
        </Elements>
      </CardContent>
    </Card>
  );
};

export default ConnectEnhancedPaymentForm;
