import React, { useEffect, useState, useRef, useCallback, useMemo } from "react";
import { FilterSearchManager, PostSearchBy } from "@/services/filtersServices";
import EmptyState from "@/components/EmptyState";
import LazyMedia from "../LazyMedia";
import { themes } from "../../../theme";
import Link from "next/link";
import { generateFileUrl } from "@/lib/utils";
import Image from "next/image";
import {
  useAccountsBulkQuery,
  usePostsQuery,
  MainContentFocus,
  PageSize,
  PostType,
} from "@/graphql/test/generated";
import { getLensProfilesById } from "@/services/lensService";
import sanitizeDStorageUrl from "@/lib/sanatizeUrl";

const Posts = ({
  searchFor,
  searchBy,
  search,
  SEARCH_FOR,
  SEARCH_BY_MAP,
  onClose,
  filters,
}: any) => {
  // Firebase posts state
  const [firebasePosts, setFirebasePosts] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const [nextStartAfterAddedAt, setNextStartAfterAddedAt] = useState<any>(undefined);
  const [error, setError] = useState<string | null>(null);

  // Lens posts state
  const [lensProfiles, setLensProfiles] = useState<Array<{ localName: string }>>([]);
  const [lensProfilesIds, setLensProfilesIds] = useState<string[]>([]);
  const [lensPosts, setLensPosts] = useState<any[]>([]);
  const [lensCurrentCursor, setLensCurrentCursor] = useState<string | null>(null);
  const [lensHasMore, setLensHasMore] = useState(true);
  const [lensLoading, setLensLoading] = useState(false);

  const containerRef = useRef<HTMLDivElement>(null);
  const PAGE_SIZE = 50;

  // Memoized search category for lens profiles
  const searchCategory = useMemo(() => {
    if (searchBy === "Description" || searchBy === "About Project" || searchBy === "Title") {
      return "art"; // Default category for content-based searches
    } else if (searchBy === "Location" || searchBy === "Geotags") {
      return "music"; // Default category for location-based searches
    }
    return "literature"; // Default fallback
  }, [searchBy]);

  // Fetch Firebase posts (initial or paginated)
  const fetchFirebasePosts = useCallback(
    async (isInitial = false) => {
      let filterBy;
      if (searchBy === "Description" || searchBy === "About Project" || searchBy === "Title") {
        filterBy = PostSearchBy.ABOUT_PROJECT;
      } else if (searchBy === "Hashtag") {
        filterBy = PostSearchBy.HASHTAGS;
      } else if (searchBy === "Location" || searchBy === "Geotags") {
        filterBy = PostSearchBy.GEOTAGS;
      } else {
        setFirebasePosts([]);
        setHasMore(false);
        setNextStartAfterAddedAt(undefined);
        return;
      }

      setLoading(true);
      setError(null);

      try {
        const resp = await FilterSearchManager.getInstance().GetPostByFilters({
          payload: {
            filterBy,
            searchTerm: search,
            limit: PAGE_SIZE,
            startAfterAddedAt: isInitial ? undefined : nextStartAfterAddedAt,
            filters,
          },
        });

        if (isInitial) {
          setFirebasePosts(resp.posts || []);
        } else {
          setFirebasePosts((prev) => [...prev, ...(resp.posts || [])]);
        }

        setNextStartAfterAddedAt(resp.nextStartAfterAddedAt);
        setHasMore(resp.hasMore);
      } catch (error: any) {
        setError(error && error.message ? error.message : "Error fetching Firebase posts");
      } finally {
        setLoading(false);
      }
    },
    [search, searchBy, nextStartAfterAddedAt, filters]
  );

  // Fetch Lens profiles based on search category
  const fetchLensProfiles = useCallback(async () => {
    try {
      const resp = await getLensProfilesById(searchCategory);
      const profiles: Array<{ localName: string }> =
        resp?.lens_ids?.map((curr: any) => ({
          localName: curr,
        })) || [];

      setLensProfiles(profiles);
    } catch (error) {
      console.error("Error fetching Lens profiles:", error);
    }
  }, [searchCategory]);

  // Lens profiles query
  const { data: lensProfileData } = useAccountsBulkQuery(
    {
      request: {
        usernames: lensProfiles,
      },
    },
    {
      refetchOnWindowFocus: false,
      enabled: lensProfiles.length > 0,
    }
  );

  // Lens posts query
  const { data: lensPostsData } = usePostsQuery(
    {
      request: {
        filter: {
          authors: lensProfilesIds,
          postTypes: [PostType.Root],
          metadata: {
            mainContentFocus: [MainContentFocus.Image, MainContentFocus.Video],
          },
        },
        cursor: lensCurrentCursor,
        pageSize: PageSize.Fifty,
      },
    },
    {
      refetchOnWindowFocus: false,
      enabled: lensProfilesIds.length > 0,
    }
  );

  // Fetch more Lens posts
  const fetchMoreLensPosts = useCallback(() => {
    if (lensPostsData?.posts?.pageInfo?.next && lensHasMore && !lensLoading) {
      setLensLoading(true);
      setLensCurrentCursor(lensPostsData.posts.pageInfo.next);
    }
  }, [lensPostsData, lensHasMore, lensLoading]);

  // Update lens profile IDs when profile data is available
  useEffect(() => {
    if (lensProfileData?.accountsBulk) {
      const ids = lensProfileData.accountsBulk.map((profile) => profile.address);
      setLensProfilesIds(ids);
    }
  }, [lensProfileData]);

  // Update lens posts when posts data is available
  useEffect(() => {
    if (lensPostsData?.posts?.items) {
      setLensPosts((prev) => {
        const existingIds = new Set(prev.map((post) => post.id));
        const newPosts = lensPostsData.posts.items.filter((post) => !existingIds.has(post.id));
        return newPosts.length > 0 ? [...prev, ...newPosts] : prev;
      });

      // Update pagination state
      setLensHasMore(!!lensPostsData.posts.pageInfo.next);
      setLensLoading(false);
    }
  }, [lensPostsData]);

  // Initial fetch or when search/searchBy changes
  useEffect(() => {
    setFirebasePosts([]);
    setLensPosts([]);
    setNextStartAfterAddedAt(undefined);
    setHasMore(true);
    setLensCurrentCursor(null);
    setLensHasMore(true);
    setLensLoading(false);

    fetchFirebasePosts(true);
    fetchLensProfiles();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [search, searchBy, filters]);

  // Helper function to get timestamp for sorting
  const getTimestampValue = useCallback((post: any): number => {
    if (post.isLensPost) {
      // Lens posts have createdAt field
      const timestamp = post.lensData?.createdAt;
      if (!timestamp) return 0;

      if (typeof timestamp === "number") return timestamp;

      try {
        const date = new Date(timestamp);
        return !isNaN(date.getTime()) ? date.getTime() : 0;
      } catch (e) {
        return 0;
      }
    } else {
      // Firebase posts have added_at or lastEditDate
      const timestamp = post.added_at || post.lastEditDate;
      if (!timestamp) return 0;

      // Firebase Timestamp has seconds property
      return timestamp.seconds ? timestamp.seconds * 1000 : 0;
    }
  }, []);

  // Merge Firebase and Lens posts with filtering and sorting
  const mergedPosts = useMemo(() => {
    const firebasePostsFormatted = firebasePosts.map((post) => ({
      ...post,
      isLensPost: false,
      displayCategory: post.category === "Storytelling" ? "Literature" : post.category,
    }));

    const lensPostsFormatted = lensPosts.map((post) => ({
      id: post.id,
      category: searchCategory.charAt(0).toUpperCase() + searchCategory.slice(1),
      displayCategory: searchCategory.charAt(0).toUpperCase() + searchCategory.slice(1),
      postFile: post.metadata?.image?.item || post.metadata?.video?.item,
      mediaType: post.metadata?.image?.item ? "image" : "video",
      user_id: post.author.address,
      isLensPost: true,
      lensData: post,
    }));

    // Combine posts
    let combined = [...firebasePostsFormatted, ...lensPostsFormatted];

    // Apply search filtering if search term exists
    if (search) {
      combined = combined.filter((post) => {
        const searchLower = search.toLowerCase();

        if (post.isLensPost) {
          // Search in Lens post metadata
          const content = post.lensData?.metadata?.content?.toLowerCase() || "";
          const title = post.lensData?.metadata?.title?.toLowerCase() || "";
          return content.includes(searchLower) || title.includes(searchLower);
        } else {
          // Search in Firebase post fields
          const title = post.title?.toLowerCase() || "";
          const description = post.description?.toLowerCase() || "";
          const hashtags = post.hashtags?.join(" ").toLowerCase() || "";
          const location = post.location?.toLowerCase() || "";

          return (
            title.includes(searchLower) ||
            description.includes(searchLower) ||
            hashtags.includes(searchLower) ||
            location.includes(searchLower)
          );
        }
      });
    }

    // Sort by timestamp (most recent first)
    return combined.sort((a, b) => {
      const timeA = getTimestampValue(a);
      const timeB = getTimestampValue(b);
      return timeB - timeA; // Descending order (newest first)
    });
  }, [firebasePosts, lensPosts, search, searchCategory, getTimestampValue]);

  // Scroll handler for backend pagination (both Firebase and Lens)
  const handleScroll = useCallback(() => {
    const container = containerRef.current;
    if (!container) return;

    const { scrollHeight, scrollTop, clientHeight } = container;
    const isNearBottom = scrollHeight - scrollTop - clientHeight < 100;

    if (isNearBottom) {
      // Load more Firebase posts if available
      if (!loading && hasMore) {
        fetchFirebasePosts(false);
      }

      // Load more Lens posts if available
      if (!lensLoading && lensHasMore && fetchMoreLensPosts) {
        fetchMoreLensPosts();
      }
    }
  }, [loading, hasMore, lensLoading, lensHasMore, fetchFirebasePosts, fetchMoreLensPosts]);

  useEffect(() => {
    const container = containerRef.current;
    if (!container) return;
    container.addEventListener("scroll", handleScroll);
    return () => container.removeEventListener("scroll", handleScroll);
  }, [handleScroll]);

  const hasPosts = Array.isArray(mergedPosts) && mergedPosts.length > 0;

  // const generateFileUrl = (postFile: string | undefined): string | undefined => {
  //   const baseUrl = process.env.BASE_STORAGE_URL;
  //   if (!baseUrl) return undefined;
  //   if (!postFile) {
  //     return undefined;
  //   }
  //   if (postFile.startsWith("https://firebasestorage.googleapis.com/")) {
  //     return postFile;
  //   }
  //   return `${baseUrl}${encodeURIComponent(postFile)}?alt=media`;
  // };

  // Enhanced media component for both Firebase and Lens posts
  const PostMedia = ({
    src,
    alt,
    border,
    mediaType,
    isLensPost,
  }: {
    src: string | undefined;
    alt: string;
    border: string;
    mediaType: string;
    isLensPost?: boolean;
  }) => {
    // Handle Lens post media URLs
    const mediaSrc = isLensPost ? sanitizeDStorageUrl(src) : src;

    return (
      <div className="relative">
        {/* Lens logo for Lens posts */}
        {isLensPost && (
          <div className="absolute top-1 right-1 z-10">
            <img
              src="/assets/lens.png"
              alt="Lens"
              className="w-6 h-6 rounded-full bg-white/80 p-1"
              loading="lazy"
            />
          </div>
        )}

        {mediaType === "video" ? (
          <LazyMedia
            src={mediaSrc}
            alt={alt}
            type="video"
            className="h-[125px] w-[125px] object-cover border-3"
            style={{ borderColor: border }}
            placeholderClassName="bg-gray-100"
            controls={false}
            autoPlay={false}
            muted={true}
            showPlayIcon={true}
            enableLightbox={true}
          />
        ) : (
          <LazyMedia
            src={mediaSrc}
            alt={alt}
            type="image"
            className="h-[125px] w-[125px] object-cover border-3"
            style={{ borderColor: border }}
            placeholderClassName="bg-gray-100"
          />
        )}
      </div>
    );
  };

  return (
    <div ref={containerRef} className="overflow-y-auto h-[calc(100vh-200px)] min-h-0">
      {hasPosts ? (
        <div className="flex flex-wrap gap-1 mt-2">
          {mergedPosts
            .filter((post: any) => post.displayCategory !== "Customer")
            .map((post: any) => {
              const matchedTheme = Object.entries(themes).find(
                ([_key, theme]: [string, any]) => post.displayCategory === theme.title
              );
              if (!matchedTheme) return null;

              // Handle media source for both Firebase and Lens posts
              const imgSrc = post.isLensPost ? post.postFile : generateFileUrl(post.postFile);
              // Generate appropriate link based on post type
              const postLink = post.isLensPost
                ? `/browse/${post.displayCategory}/${post.id}%20${post.user_id}`
                : `/browse/${post.category || post.displayCategory}/${post.id}%20${post.user_id}`;

              return (
                <div
                  key={`${post.isLensPost ? "lens" : "firebase"}-${post.id}`}
                  className="relative flex flex-col items-center mb-0"
                  style={{ width: 125, height: 125 }}
                >
                  <div className="relative" style={{ width: 125, height: 125 }}>
                    <Link href={postLink} className="w-full text-left" onClick={onClose}>
                      <PostMedia
                        src={imgSrc}
                        mediaType={post.mediaType || "image"}
                        alt={`Post ${post.id}`}
                        border={matchedTheme[1].backgroundColor}
                        isLensPost={post.isLensPost}
                      />
                    </Link>
                  </div>
                </div>
              );
            })}
        </div>
      ) : loading ? (
        <div className="flex justify-center items-center h-full py-8">
          <span className="loader">Loading...</span>
        </div>
      ) : (
        <div className="flex justify-center items-center h-full">
          <EmptyState
            type="posts"
            title="No posts found"
            message="Try adjusting your search or filter to find posts."
          />
        </div>
      )}
      {/* Show loading spinner at bottom if loading more */}
      {hasPosts && (loading || lensLoading) && (
        <div className="flex justify-center items-center py-4">
          <span className="loader">Loading more posts...</span>
        </div>
      )}
      {/* Show 'No more data found' if all data loaded */}
      {hasPosts && !hasMore && !lensHasMore && !loading && !lensLoading && (
        <div className="flex justify-center items-center py-4 text-gray-500">
          No more posts available
        </div>
      )}
      {/* Show error if any */}
      {error && <div className="text-red-500 text-center py-2">{error}</div>}
    </div>
  );
};

export default Posts;
