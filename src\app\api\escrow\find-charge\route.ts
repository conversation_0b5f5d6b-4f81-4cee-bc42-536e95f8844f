import { NextRequest, NextResponse } from 'next/server';
import { Stripe } from 'stripe';

if (!process.env.STRIPE_SECRET_KEY) {
  throw new Error("STRIPE_SECRET_KEY is not defined");
}

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY);

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const currency = searchParams.get('currency') || 'gbp';
    const amount = searchParams.get('amount');
    const limit = parseInt(searchParams.get('limit') || '10');

    console.log('Finding charges with criteria:', { currency, amount, limit });

    // Get recent charges
    const charges = await stripe.charges.list({
      limit: Math.min(limit, 100), // Max 100 charges
      created: {
        gte: Math.floor(Date.now() / 1000) - (7 * 24 * 60 * 60) // Last 7 days
      }
    });

    // Filter charges by criteria
    let filteredCharges = charges.data.filter(charge => {
      const currencyMatch = charge.currency === currency;
      const amountMatch = amount ? charge.amount === parseInt(amount) : true;
      const statusMatch = charge.status === 'succeeded';
      
      return currencyMatch && amountMatch && statusMatch;
    });

    // Format the results
    const chargeResults = filteredCharges.map(charge => ({
      id: charge.id,
      amount: charge.amount,
      amountInMajorUnit: charge.amount / 100,
      currency: charge.currency,
      status: charge.status,
      created: new Date(charge.created * 1000).toISOString(),
      description: charge.description,
      customer: charge.customer,
      payment_intent: charge.payment_intent,
      metadata: charge.metadata
    }));

    return NextResponse.json({
      success: true,
      totalFound: chargeResults.length,
      charges: chargeResults,
      searchCriteria: {
        currency,
        amount: amount ? parseInt(amount) : 'any',
        limit,
        timeRange: 'last 7 days'
      },
      message: chargeResults.length > 0 
        ? `Found ${chargeResults.length} matching charges`
        : 'No charges found matching the criteria'
    });

  } catch (error) {
    console.error('Error finding charges:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    return NextResponse.json({ 
      error: 'Failed to find charges',
      details: errorMessage
    }, { status: 500 });
  }
}

// POST endpoint to validate a specific charge against transaction requirements
export async function POST(request: NextRequest) {
  try {
    const { chargeId, expectedAmount, expectedCurrency } = await request.json();

    if (!chargeId) {
      return NextResponse.json({
        error: 'chargeId is required'
      }, { status: 400 });
    }

    // Get the charge details
    const charge = await stripe.charges.retrieve(chargeId);

    // Validate the charge
    const validation = {
      chargeId: charge.id,
      amount: charge.amount,
      amountInMajorUnit: charge.amount / 100,
      currency: charge.currency,
      status: charge.status,
      created: new Date(charge.created * 1000).toISOString(),
      
      // Validation results
      isValid: true,
      issues: [] as string[],
      
      // Expected vs actual
      expectedAmount,
      expectedCurrency,
      amountMatches: expectedAmount ? charge.amount === expectedAmount : null,
      currencyMatches: expectedCurrency ? charge.currency === expectedCurrency : null
    };

    // Check for issues
    if (charge.status !== 'succeeded') {
      validation.isValid = false;
      validation.issues.push(`Charge status is '${charge.status}', expected 'succeeded'`);
    }

    if (expectedAmount && charge.amount !== expectedAmount) {
      validation.isValid = false;
      validation.issues.push(`Amount mismatch: charge has ${charge.amount}, expected ${expectedAmount}`);
    }

    if (expectedCurrency && charge.currency !== expectedCurrency) {
      validation.isValid = false;
      validation.issues.push(`Currency mismatch: charge is in ${charge.currency}, expected ${expectedCurrency}`);
    }

    return NextResponse.json({
      success: true,
      validation,
      message: validation.isValid 
        ? 'Charge is valid for escrow transfer'
        : `Charge validation failed: ${validation.issues.join(', ')}`
    });

  } catch (error) {
    console.error('Error validating charge:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    return NextResponse.json({ 
      error: 'Failed to validate charge',
      details: errorMessage
    }, { status: 500 });
  }
}
