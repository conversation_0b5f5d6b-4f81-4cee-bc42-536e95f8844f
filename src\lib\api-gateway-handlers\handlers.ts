import { MailServiceManager } from "@/services/MailService";
import { getAuth } from "firebase-admin/auth";
import { FieldValue, getFirestore } from "firebase-admin/firestore";
import { BASE_URL } from "../utils";

export const SendMailHandler = async (body: any, uid?: string) => {
  try {
    const db = getFirestore();
    const mailRef = db.collection("mail");

    if (body?.mail_type === "verify_email") {
      let link = await getAuth().generateEmailVerificationLink(body?.payload?.to, {
        url: BASE_URL,
        handleCodeInApp: true,
      });
      body.message = {
        ...body?.message,
        verificationUrl: link,
      };
    };

    if(body?.mail_type === "reset_password") { 
      const link = await getAuth().generatePasswordResetLink(body?.payload?.to, {
      url: BASE_URL,
      handleCodeInApp: true, // optional
    });      
      body.message = {
        ...body?.message,
        resetPasswordUrl: link,
      };
    }

    const { from, to, subject, html } = MailServiceManager.getInstance().buildEmail({
      to: body?.payload?.to,
      type: body?.mail_type,
      data: body?.message,
    });

    let payload = {
      to,
      from,
      message: {
        subject,
        html,
        text: html.replace(/<[^>]+>/g, ""),
      },
      createdAt: FieldValue.serverTimestamp(),
    };

    await mailRef.add(payload);

    return "Mail Sent Successfully";
  } catch (error) {
    console.log({ error });
  }
};
