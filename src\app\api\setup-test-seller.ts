import type { NextApiRequest, NextApiResponse } from 'next';
import { Stripe } from 'stripe';
import { db } from '@/lib/firebaseAdmin';

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY as string);

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  try {
    if (req.method !== 'POST') {
      return res.status(405).json({ error: 'Method not allowed' });
    }

    // Create a real Stripe Express account for testing
    const account = await stripe.accounts.create({
      type: 'express',
      country: 'US', // You can change this based on your needs
      email: '<EMAIL>'
    });

    const sellerId = 'seller_123';

    // Save the real Stripe account ID to Firestore
    await db.collection('sellers').doc(sellerId).set({
      stripeAccountId: account.id,
      name: 'Test Seller',
      email: '<EMAIL>',
      createdAt: new Date().toISOString(),
      onboardingComplete: false // Will be true after onboarding
    });

    res.status(200).json({
      message: 'Test seller created successfully with real Stripe account',
      sellerId,
      stripeAccountId: account.id,
      note: 'This is a real Stripe Express account, but it needs onboarding to accept payments'
    });
  } catch (error) {
    console.error('Error setting up test seller:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    res.status(500).json({ error: errorMessage });
  }
}
