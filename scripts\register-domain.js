#!/usr/bin/env node

/**
 * Script to register domains with Stripe for Apple Pay, Google Pay, etc.
 * 
 * Usage:
 * node scripts/register-domain.js your-domain.com
 * node scripts/register-domain.js localhost:3000 (for development)
 * 
 * Make sure to set STRIPE_SECRET_KEY in your environment variables
 */

const stripe = require('stripe')(process.env.STRIPE_SECRET_KEY);

async function registerDomain(domainName) {
  if (!domainName) {
    console.error('❌ Please provide a domain name');
    console.log('Usage: node scripts/register-domain.js your-domain.com');
    process.exit(1);
  }

  if (!process.env.STRIPE_SECRET_KEY) {
    console.error('❌ STRIPE_SECRET_KEY environment variable is required');
    process.exit(1);
  }

  try {
    console.log(`🔄 Registering domain: ${domainName}`);
    
    const paymentMethodDomain = await stripe.paymentMethodDomains.create({
      domain_name: domainName,
    });

    console.log('✅ Domain registered successfully!');
    console.log('📋 Domain details:');
    console.log(`   ID: ${paymentMethodDomain.id}`);
    console.log(`   Domain: ${paymentMethodDomain.domain_name}`);
    console.log(`   Enabled: ${paymentMethodDomain.enabled}`);
    console.log(`   Created: ${new Date(paymentMethodDomain.created * 1000).toISOString()}`);
    
  } catch (error) {
    if (error.code === 'resource_already_exists') {
      console.log(`✅ Domain ${domainName} is already registered`);
    } else {
      console.error('❌ Error registering domain:', error.message);
      console.error('   Type:', error.type);
      console.error('   Code:', error.code);
    }
  }
}

async function listDomains() {
  try {
    console.log('🔄 Fetching registered domains...');
    
    const domains = await stripe.paymentMethodDomains.list({
      limit: 100,
    });

    if (domains.data.length === 0) {
      console.log('📭 No domains registered yet');
      return;
    }

    console.log(`📋 Found ${domains.data.length} registered domain(s):`);
    domains.data.forEach((domain, index) => {
      console.log(`   ${index + 1}. ${domain.domain_name} (${domain.enabled ? 'enabled' : 'disabled'})`);
    });
    
  } catch (error) {
    console.error('❌ Error fetching domains:', error.message);
  }
}

// Main execution
const command = process.argv[2];

if (command === 'list') {
  listDomains();
} else if (command) {
  registerDomain(command);
} else {
  console.log('🍎 Stripe Domain Registration Tool');
  console.log('');
  console.log('Usage:');
  console.log('  node scripts/register-domain.js your-domain.com    # Register a domain');
  console.log('  node scripts/register-domain.js list              # List registered domains');
  console.log('');
  console.log('Examples:');
  console.log('  node scripts/register-domain.js example.com');
  console.log('  node scripts/register-domain.js www.example.com');
  console.log('  node scripts/register-domain.js localhost:3000');
  console.log('');
  console.log('Note: Make sure STRIPE_SECRET_KEY is set in your environment');
}
