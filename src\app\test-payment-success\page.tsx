"use client";

import { useState } from 'react';

export default function TestPaymentSuccess() {
  const [result, setResult] = useState<any>(null);
  const [loading, setLoading] = useState(false);

  const testPaymentSuccess = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/payment/success', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          paymentIntentId: 'pi_test_123456789',
          orderId: 'VEBxHJCGZcCPOGK0c8Jy', // Use a real order ID from your logs
          transactionId: '2QT3SXm3KqFr5AivL5hv', // Use a real transaction ID from your logs
          amount: 2848,
          currency: 'gbp',
          isEscrow: true,
          userId: 'ls6gz7E801dmCXyny5iKfcG8iBa2', // Use a real user ID
          sellerId: 'KxUypdjcl5bDwURhWM2Q6cqDIT02', // Use a real seller ID
          userEmail: '<EMAIL>',
          userName: 'Test User',
        }),
      });

      const data = await response.json();
      setResult(data);
    } catch (error) {
      setResult({ error: error.message });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="p-8">
      <h1 className="text-2xl font-bold mb-4">Test Payment Success API</h1>
      
      <button
        onClick={testPaymentSuccess}
        disabled={loading}
        className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 disabled:bg-gray-400"
      >
        {loading ? 'Testing...' : 'Test Payment Success API'}
      </button>

      {result && (
        <div className="mt-4 p-4 bg-gray-100 rounded">
          <h2 className="font-bold mb-2">Result:</h2>
          <pre className="text-sm overflow-auto">
            {JSON.stringify(result, null, 2)}
          </pre>
        </div>
      )}
    </div>
  );
}
