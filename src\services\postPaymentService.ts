import { OrderStatusType, OrderInfo } from '@/lib/constant';
import { updateOrder, UpdateActivityLog, updateOrderWithEscrow } from './ordersServices';
import { updateTransaction } from './transactionService';
import { Timestamp } from 'firebase/firestore';

export interface PaymentSuccessData {
  paymentIntentId: string;
  orderId: string;
  transactionId?: string;
  amount: number;
  currency: string;
  isEscrow: boolean;
  userId: string;
  sellerId: string;
  userEmail?: string;
  userName?: string;
  sellerName?: string;
  chargeId?: string;
}

export interface OrderStatusUpdate {
  orderId: string;
  status: OrderStatusType;
  paymentIntentId?: string;
  transactionId?: string;
  chargeId?: string;
  userId: string;
  sellerId: string;
  userName?: string;
  sellerName?: string;
  reason?: string;
  comment?: string;
}

/**
 * Main function to handle post-payment processing
 * This orchestrates all the necessary database updates and status changes
 */
export const processPaymentSuccess = async (data: PaymentSuccessData) => {
  console.log('🎉 Starting post-payment processing...', data);
  
  try {
    // Step 1: Update order with payment details
    await updateOrderPaymentDetails(data);
    
    // Step 2: Update transaction status
    if (data.transactionId) {
      await updateTransactionStatus(data);
    }
    
    // Step 3: Update order status based on payment type
    if (data.isEscrow) {
      await processEscrowPaymentSuccess(data);
    } else {
      await processRegularPaymentSuccess(data);
    }
    
    // Step 4: Create activity logs
    await createPaymentActivityLogs(data);
    
    console.log('✅ Post-payment processing completed successfully');
    return { success: true };
    
  } catch (error) {
    console.error('❌ Error in post-payment processing:', error);
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Unknown error' 
    };
  }
};

/**
 * Update order with payment details
 */
const updateOrderPaymentDetails = async (data: PaymentSuccessData) => {
  console.log('📝 Updating order with payment details...');

  // First, let's check the current order status
  const { getOrderById } = await import('./ordersServices');
  try {
    const currentOrder = await getOrderById(data.orderId);
    console.log('📋 Current order status before update:', currentOrder?.status);
  } catch (error) {
    console.log('⚠️ Could not fetch current order status:', error);
  }
  
  const updateData = {
    payment_intent_id: data.paymentIntentId,
    ...(data.transactionId && { transactionId: data.transactionId }),
    ...(data.chargeId && { chargeId: data.chargeId }),
    currency: data.currency,
    // Add payment completion timestamp
    paymentCompletedAt: Timestamp.now(),
  };
  
  const result = await updateOrder(data.orderId, updateData);
  
  if (!result.success) {
    throw new Error(`Failed to update order payment details: ${result.error}`);
  }
  
  console.log('✅ Order payment details updated');
};

/**
 * Update transaction status to completed
 */
const updateTransactionStatus = async (data: PaymentSuccessData) => {
  console.log('💳 Updating transaction status...');
  
  const updateData = {
    status: 'completed',
    stripePaymentIntentId: data.paymentIntentId,
    paymentCaptured: true,
    paymentCapturedAt: new Date(),
    completedAt: new Date(),
    metadata: {
      paymentCompletedAt: new Date().toISOString(),
      orderId: data.orderId,
      amount: data.amount,
      currency: data.currency,
    }
  };
  
  const result = await updateTransaction(data.transactionId!, updateData);
  
  if (!result.success) {
    throw new Error(`Failed to update transaction: ${result.error}`);
  }
  
  console.log('✅ Transaction status updated');
};

/**
 * Process escrow payment success
 * For escrow payments, the order goes to "NEW" status and payment is authorized but not captured
 */
const processEscrowPaymentSuccess = async (data: PaymentSuccessData) => {
  console.log('🔒 Processing escrow payment success...');
  
  // For escrow payments, set status to NEW (payment authorized, awaiting seller acceptance)
  const statusUpdate: OrderStatusUpdate = {
    orderId: data.orderId,
    status: OrderStatusType.NEW,
    paymentIntentId: data.paymentIntentId,
    transactionId: data.transactionId,
    chargeId: data.chargeId,
    userId: data.userId,
    sellerId: data.sellerId,
    userName: data.userName,
    sellerName: data.sellerName,
    reason: 'Escrow payment authorized successfully',
    comment: 'Payment has been authorized and funds are held in escrow. Awaiting seller acceptance.'
  };
  
  await updateOrderStatus(statusUpdate);
  console.log('✅ Escrow payment processed - Order status set to NEW');
};

/**
 * Process regular (non-escrow) payment success
 * For regular payments, the order can go directly to a more advanced status
 */
const processRegularPaymentSuccess = async (data: PaymentSuccessData) => {
  console.log('💰 Processing regular payment success...');
  
  // For regular payments, set status to ACCEPTED (payment completed)
  const statusUpdate: OrderStatusUpdate = {
    orderId: data.orderId,
    status: OrderStatusType.ACCEPTED,
    paymentIntentId: data.paymentIntentId,
    transactionId: data.transactionId,
    chargeId: data.chargeId,
    userId: data.userId,
    sellerId: data.sellerId,
    userName: data.userName,
    sellerName: data.sellerName,
    reason: 'Payment completed successfully',
    comment: 'Payment has been processed successfully. Order is ready to begin.'
  };
  
  await updateOrderStatus(statusUpdate);
  console.log('✅ Regular payment processed - Order status set to ACCEPTED');
};

/**
 * Update order status and create activity logs
 */
const updateOrderStatus = async (update: OrderStatusUpdate) => {
  console.log(`📊 Updating order status to ${update.status}...`);
  
  // Use the existing UpdateActivityLog function which handles both status update and activity logging
  await UpdateActivityLog({
    orderId: update.orderId,
    description: update.comment || '',
    from: "user",
    title: update.status,
    loggedInUser: update.userName || 'Customer',
    sellerName: update.sellerName || 'Seller',
    userName: update.userName || 'Customer',
    reason: update.reason || 'Payment completed',
  });
  
  console.log(`✅ Order status updated to ${update.status}`);
};

/**
 * Create payment-specific activity logs
 */
const createPaymentActivityLogs = async (data: PaymentSuccessData) => {
  console.log('📋 Creating payment activity logs...');
  
  // Create a payment completion log
  const paymentLogData = {
    orderId: data.orderId,
    description: `Payment of ${data.currency.toUpperCase()} ${(data.amount / 100).toFixed(2)} completed successfully${data.isEscrow ? ' (Escrow)' : ''}`,
    from: "user" as const,
    title: data.isEscrow ? OrderStatusType.NEW : OrderStatusType.ACCEPTED,
    loggedInUser: data.userName || 'Customer',
    sellerName: data.sellerName || 'Seller',
    userName: data.userName || 'Customer',
    reason: `Payment Intent: ${data.paymentIntentId}`,
  };
  
  // The UpdateActivityLog function will handle creating the activity log entry
  // We don't need to call it again here since updateOrderStatus already does this
  
  console.log('✅ Payment activity logs created');
};

/**
 * Handle payment failure scenarios
 */
export const processPaymentFailure = async (data: {
  orderId: string;
  transactionId?: string;
  error: string;
  userId: string;
  sellerId: string;
  userName?: string;
  sellerName?: string;
}) => {
  console.log('❌ Processing payment failure...', data);
  
  try {
    // Update order status to INCOMPLETE
    const statusUpdate: OrderStatusUpdate = {
      orderId: data.orderId,
      status: OrderStatusType.INCOMPLETE,
      userId: data.userId,
      sellerId: data.sellerId,
      userName: data.userName,
      sellerName: data.sellerName,
      reason: 'Payment failed',
      comment: `Payment failed: ${data.error}`,
    };
    
    await updateOrderStatus(statusUpdate);
    
    // Update transaction if exists
    if (data.transactionId) {
      await updateTransaction(data.transactionId, {
        status: 'failed',
        metadata: {
          error: data.error,
          failedAt: new Date().toISOString(),
        }
      });
    }
    
    console.log('✅ Payment failure processed');
    return { success: true };
    
  } catch (error) {
    console.error('❌ Error processing payment failure:', error);
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Unknown error' 
    };
  }
};

/**
 * Get order details for post-payment redirect
 */
export const getOrderForRedirect = async (orderId: string) => {
  try {
    // You can implement this to fetch order details for the success page
    // This would use your existing order fetching functions
    return { success: true, orderId };
  } catch (error) {
    console.error('Error fetching order for redirect:', error);
    return { success: false, error: 'Failed to fetch order details' };
  }
};
