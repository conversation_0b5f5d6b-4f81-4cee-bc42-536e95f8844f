"use client";

import React, { useState, useEffect } from "react";
import { loadStripe } from "@stripe/stripe-js";
import {
  Elements,
  PaymentElement,
  ExpressCheckoutElement,
  useStripe,
  useElements,
} from "@stripe/react-stripe-js";
import { SimplePaymentServiceCard } from "./SimplePaymentServiceCard";

const stripePromise = loadStripe(process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY!);

interface SimplePaymentFormProps {
  paymentIntentId?: string;
  amount: number;
  currency: string;
  productName: string;
  isEscrow?: boolean;
  orderId?: string;
  transactionId?: string;
  userId?: string;
  sellerId?: string;
  onSuccess?: (paymentIntent: any) => void;
  onError?: (error: string) => void;
  uiMode?: "wallet" | "card" | "crypto";
}

function CheckoutForm({
  orderId,
  transactionId,
  userId,
  sellerId,
  amount,
  currency,
  productName,
  isEscrow,
  clientSecret,
  onSuccess,
  onError,
  uiMode = "card",
}: {
  orderId?: string;
  transactionId?: string;
  userId?: string;
  sellerId?: string;
  amount: number;
  currency: string;
  productName: string;
  isEscrow: boolean;
  clientSecret?: string;
  uiMode?: "wallet" | "card" | "crypto";
  onSuccess?: (paymentIntent: any) => void;
  onError?: (error: string) => void;
}) {
  const stripe = useStripe();
  const elements = useElements();
  const [isLoading, setIsLoading] = useState(false);
  const [message, setMessage] = useState<string | null>(null);
  const [expressCheckoutMessage, setExpressCheckoutMessage] = useState<string | null>(null);
  const [expressCheckoutReady, setExpressCheckoutReady] = useState(false);

  // Debug: Check Stripe instance when it becomes available
  useEffect(() => {
    if (stripe) {
      console.log("🔍 Stripe instance available in CheckoutForm:", stripe);
    } else {
      console.log("❌ Stripe instance not available in CheckoutForm");
    }
  }, [stripe]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!stripe || !elements) {
      return;
    }

    setIsLoading(true);
    setMessage(null);

    // Use redirect: "if_required" to handle success in our callback
    const returnUrl = new URL(`${window.location.origin}/payment-success`);
    if (orderId) returnUrl.searchParams.set("order_id", orderId);
    if (transactionId) returnUrl.searchParams.set("transaction_id", transactionId);
    if (userId) returnUrl.searchParams.set("user_id", userId);
    if (sellerId) returnUrl.searchParams.set("seller_id", sellerId);
    returnUrl.searchParams.set("amount", amount.toString());
    returnUrl.searchParams.set("currency", currency);
    returnUrl.searchParams.set("is_escrow", isEscrow.toString());

    // Submit the form first (required by Stripe)
    const submitResult = await elements.submit();
    if (submitResult.error) {
      setMessage(submitResult.error.message || "Form validation failed");
      onError?.(submitResult.error.message || "Form validation failed");
      setIsLoading(false);
      return;
    }

    // Check if we already have a client secret from existing payment intent
    try {
      let clientSecretToUse = clientSecret; // Use existing client secret if available

      // Only create new payment intent if we don't have an existing client secret
      if (!clientSecretToUse) {
        console.log("🔄 Creating NEW payment intent with data:", {
          amount,
          currency,
          userId,
          sellerId,
          orderId,
          isEscrow,
        });

        // Use the correct endpoint based on escrow setting
        // For crypto (non-escrow), use our new /api/payment endpoint that creates a PI with payment_method_types=['crypto']
        const endpoint = isEscrow ? "/api/escrow/create-payment-intent" : "/api/payment";
        console.log("🎯 Using endpoint:", endpoint);

        const response = await fetch(endpoint, {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({
            amount,
            currency,
            userId,
            sellerId,
            orderId,
            isEscrow,
            paymentMethodType: isEscrow ? undefined : "crypto",
            productName,
          }),
        });

        console.log("📡 Payment intent response status:", response.status);

        const data = await response.json();
        console.log("📄 Payment intent response data:", data);

        if (!data.success) {
          throw new Error(data.error || "Failed to create payment intent");
        }

        clientSecretToUse = data.clientSecret;
      } else {
        console.log("✅ Using existing client secret from payment intent");
      }

      if (!clientSecretToUse) {
        throw new Error("No client secret available for payment confirmation");
      }

      const { error, paymentIntent } = await stripe.confirmPayment({
        elements,
        clientSecret: clientSecretToUse,
        confirmParams: {
          return_url: returnUrl.toString(),
        },
        redirect: "if_required",
      });

      if (error) {
        console.error("❌ Payment confirmation error:", error);
        console.error("   Error type:", error.type);
        console.error("   Error code:", error.code);
        console.error("   Error message:", error.message);

        if (error.type === "card_error" || error.type === "validation_error") {
          setMessage(error.message || "An error occurred");
          onError?.(error.message || "An error occurred");
        } else {
          const errorMsg = `Payment failed: ${error.message || "An unexpected error occurred"}`;
          setMessage(errorMsg);
          onError?.(errorMsg);
        }
      } else if (paymentIntent) {
        // Payment succeeded without redirect - handle success
        if (paymentIntent.status === "succeeded") {
          setMessage("Payment succeeded!");
          onSuccess?.(paymentIntent);
        } else if (paymentIntent.status === "requires_capture") {
          setMessage("Payment authorized! (Escrow)");
          onSuccess?.(paymentIntent);
        } else if (paymentIntent.status === "processing") {
          setMessage("Payment is processing...");
          // You might want to poll for status or handle this case
        } else {
          setMessage(`Payment status: ${paymentIntent.status}`);
        }
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Unknown error";
      setMessage(errorMessage);
      onError?.(errorMessage);
    }

    setIsLoading(false);
  };

  // Enhanced logging for Apple Pay debugging
  console.log("🍎 APPLE PAY DEBUGGING");
  console.log("🔍 Payment details:", { amount, currency, isEscrow });
  console.log("🔍 Current domain:", window.location.hostname);
  console.log("🔍 Protocol:", window.location.protocol);
  console.log("🔍 User Agent:", navigator.userAgent);
  console.log(
    "🔍 Apple Pay Session available:",
    typeof (window as any).ApplePaySession !== "undefined"
  );

  if (typeof (window as any).ApplePaySession !== "undefined") {
    console.log(
      "🔍 Apple Pay can make payments:",
      (window as any).ApplePaySession.canMakePayments()
    );
    console.log(
      "🔍 Apple Pay version:",
      (window as any).ApplePaySession.supportsVersion ? "Supported" : "Not supported"
    );
  }

  const handleExpressCheckout = async (event: any) => {
    console.log("🚀 Express checkout initiated:", event);
    try {
      // Prefer existing escrow clientSecret when provided (wallet/card use pre-created PI per requirements)
      let clientSecretToUse = clientSecret;

      if (!clientSecretToUse) {
        // Fallback: create a PaymentIntent now (used in non-escrow mode or if page loaded without pre-PI)
        const endpoint = isEscrow ? "/api/escrow/create-payment-intent" : "/api/payment";
        console.log("🎯 Express checkout creating PI via:", endpoint);

        const response = await fetch(endpoint, {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({
            amount,
            currency,
            userId,
            sellerId,
            orderId,
            isEscrow,
            paymentMethodType: isEscrow ? undefined : "crypto",
            productName,
          }),
        });
        const data = await response.json();
        if (!data.success) {
          throw new Error(data.error || "Failed to create payment intent");
        }
        clientSecretToUse = data.clientSecret;
      }

      // Submit the form first (required by Stripe)
      const submitResult = await elements!.submit();
      if (submitResult.error) {
        setExpressCheckoutMessage(submitResult.error.message || "Form validation failed");
        onError?.(submitResult.error.message || "Form validation failed");
        return;
      }

      const { error, paymentIntent } = await stripe!.confirmPayment({
        elements: elements!,
        clientSecret: clientSecretToUse!,
        confirmParams: { return_url: `${window.location.origin}/payment-success` },
        redirect: "if_required",
      });

      if (error) {
        setExpressCheckoutMessage(error.message || "Express checkout failed");
        onError?.(error.message || "Express checkout failed");
      } else if (paymentIntent) {
        if (paymentIntent.status === "succeeded") {
          setExpressCheckoutMessage("Payment succeeded!");
          onSuccess?.(paymentIntent);
        } else if (paymentIntent.status === "requires_capture") {
          setExpressCheckoutMessage("Payment authorized! (Escrow)");
          onSuccess?.(paymentIntent);
        }
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Unknown error";
      setExpressCheckoutMessage(errorMessage);
      onError?.(errorMessage);
    }
  };

  return (
    <div className=" ">
      {/* Right side - Payment Form */}
      <div className="space-y-6 w-full">
        <h2 className="text-2xl font-bold text-gray-900">Complete Payment</h2>

        <form id="payment-form" onSubmit={handleSubmit}>
          {/* Express Checkout Element (Apple Pay/Google Pay/Link) */}
          {uiMode === "wallet" && (
            <div className="mb-4">
              <ExpressCheckoutElement
                onConfirm={handleExpressCheckout}
                onReady={({ availablePaymentMethods }) => {
                  console.log("🚀 Express Checkout ready!");
                  console.log("💳 Available payment methods:", availablePaymentMethods);
                  console.log("🔍 Current URL:", window.location.href);
                  console.log("🔍 Domain:", window.location.hostname);
                  console.log("🔍 Protocol:", window.location.protocol);

                  if (availablePaymentMethods && Object.keys(availablePaymentMethods).length > 0) {
                    setExpressCheckoutReady(true);
                    console.log(
                      "✅ Payment methods available:",
                      Object.keys(availablePaymentMethods)
                    );

                    // Log each available payment method with details
                    Object.entries(availablePaymentMethods).forEach(([method, details]) => {
                      console.log(`🔹 ${method}:`, details);

                      // Special logging for Apple Pay
                      if (method === "applePay") {
                        console.log("🍎 Apple Pay Details:");
                        console.log("   - Available:", true);
                        console.log(
                          "   - Browser:",
                          navigator.userAgent.includes("Safari") ? "Safari" : "Other"
                        );
                        console.log(
                          "   - ApplePaySession:",
                          typeof (window as any).ApplePaySession !== "undefined"
                        );
                        if (typeof (window as any).ApplePaySession !== "undefined") {
                          console.log(
                            "   - Can Make Payments:",
                            (window as any).ApplePaySession.canMakePayments()
                          );
                        }
                      }
                    });
                  } else {
                    console.log("❌ No payment methods available");
                    setExpressCheckoutReady(false);
                  }
                }}
                onLoadError={(event) => {
                  console.log("❌ Express Checkout load error:", event);
                }}
                options={{
                  paymentMethods: {
                    applePay: "auto",
                    googlePay: "auto",
                    link: "auto",
                    paypal: "auto",
                  },
                  buttonTheme: {
                    applePay: "black",
                    googlePay: "black",
                  },
                  layout: { maxColumns: 3, maxRows: 1, overflow: "auto" },
                }}
              />
            </div>
          )}
          {/* Show divider only when not in wallets-only mode */}
          {uiMode !== "wallet" &&
            (expressCheckoutReady ? (
              <div>
                <div className="flex items-center my-4">
                  <div className="flex-1 border-t border-gray-300"></div>
                  <span className="px-3 text-gray-500 text-sm">or pay with card</span>
                  <div className="flex-1 border-t border-gray-300"></div>
                </div>
              </div>
            ) : (
              <div></div>
            ))}

          {uiMode !== "wallet" && (
            <>
              <PaymentElement
                id="payment-element"
                options={{
                  layout: {
                    type: "tabs",
                    defaultCollapsed: false,
                    radios: false,
                    spacedAccordionItems: true,
                  },
                }}
              />
              <button
                disabled={isLoading || !stripe || !elements}
                id="submit"
                className="w-full mt-6 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white font-bold py-3 px-4 rounded transition-colors"
              >
                <span id="button-text">
                  {isLoading ? (
                    <div className="flex items-center justify-center">
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                      Processing...
                    </div>
                  ) : (
                    "Pay now"
                  )}
                </span>
              </button>
              {message && (
                <div
                  className={`mt-4 p-3 rounded ${
                    message.includes("processing")
                      ? "bg-blue-100 text-blue-700 border border-blue-400"
                      : "bg-red-100 text-red-700 border border-red-400"
                  }`}
                >
                  {message}
                </div>
              )}
            </>
          )}
        </form>
      </div>
    </div>
  );
}

export default function SimplePaymentForm({
  paymentIntentId,
  amount,
  currency,
  productName,
  isEscrow = false,
  orderId,
  transactionId,
  userId,
  sellerId,
  onSuccess,
  onError,
  uiMode = "card",
}: SimplePaymentFormProps) {
  const [clientSecret, setClientSecret] = useState("");
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  //   // Load Google Pay API
  //   useEffect(() => {
  //     const loadGooglePayAPI = () => {
  //       if (typeof window !== 'undefined' && !(window as any).google?.payments?.api) {
  //         const script = document.createElement('script');
  //         script.src = 'https://pay.google.com/gp/p/js/pay.js';
  //         script.async = true;
  //         script.onload = () => {
  //           console.log('✅ Google Pay API loaded successfully');
  //         };
  //         script.onerror = () => {
  //           console.log('❌ Failed to load Google Pay API');
  //         };
  //         document.head.appendChild(script);
  //       }
  //     };

  //     loadGooglePayAPI();
  //   }, []);
  // console.log('Hi -->',currency);
  useEffect(() => {
    // Debug: Check if Google Pay is available

    if (typeof window !== "undefined") {
      console.log("🔍 Window object available");
      console.log("🔍 Google object:", (window as any).google);

      if ((window as any).google?.payments?.api) {
        console.log("🔍 Google Pay API available");

        // Test Google Pay readiness
        const paymentsClient = new (window as any).google.payments.api.PaymentsClient({
          environment: "TEST", // Change to 'PRODUCTION' for live
        });

        const isReadyToPayRequest = {
          apiVersion: 2,
          apiVersionMinor: 0,
          allowedPaymentMethods: [
            {
              type: "CARD",
              parameters: {
                allowedAuthMethods: ["PAN_ONLY", "CRYPTOGRAM_3DS"],
                allowedCardNetworks: ["MASTERCARD", "VISA"],
              },
            },
          ],
        };

        paymentsClient
          .isReadyToPay(isReadyToPayRequest)
          .then((response: any) => {
            console.log("🔍 Google Pay readiness:", response);
          })
          .catch((error: any) => {
            console.log("❌ Google Pay readiness error:", error);
          });
      } else {
        console.log("❌ Google Pay API not available");
        console.log("🔍 Available on window:", Object.keys(window as any));
      }
    }

    const getPaymentIntent = async () => {
      try {
        if (paymentIntentId) {
          console.log("🔄 Retrieving existing payment intent...", paymentIntentId);

          const response = await fetch(`/api/payment-intent/${paymentIntentId}`, {
            method: "GET",
            headers: { "Content-Type": "application/json" },
          });

          if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
          }

          const data = await response.json();
          console.log("✅ Payment intent retrieved:", data);

          if (data.clientSecret) {
            setClientSecret(data.clientSecret);
            console.log("✅ Client secret set");
          } else {
            throw new Error(data.error || "No client secret received");
          }
        } else {
          // No existing payment intent provided. That's OK for non-escrow flows or when creating PI at submit time.
          console.log("ℹ️ No paymentIntentId provided; proceeding with mode-based Elements.");
          // Do not set an error; we'll allow CheckoutForm to create a PI on submit.
        }
      } catch (err) {
        console.error("❌ Error retrieving payment intent:", err);
        const errorMessage = err instanceof Error ? err.message : "Unknown error";
        setError(errorMessage);
        onError?.(errorMessage);
      } finally {
        setLoading(false);
      }
    };

    getPaymentIntent();
  }, [paymentIntentId, onError]);

  if (loading) {
    return (
      <div className="flex flex-col items-center justify-center p-8 space-y-4">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        <span className="text-gray-600">Loading payment form...</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-6 bg-red-50 border border-red-200 text-red-700 rounded-lg">
        <h3 className="font-semibold text-lg mb-2">Payment Error</h3>
        <p className="mb-4">{error}</p>
        <button
          onClick={() => window.location.reload()}
          className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 transition-colors"
        >
          Try Again
        </button>
      </div>
    );
  }

  // If we require a pre-created PI (wallet/card escrow), wait for clientSecret.
  if (isEscrow && (uiMode === "wallet" || uiMode === "card") && !clientSecret) {
    return (
      <div className="p-6 bg-yellow-50 border border-yellow-200 text-yellow-700 rounded-lg">
        <p>Initializing payment form...</p>
      </div>
    );
  }

  const appearance = {
    theme: "stripe" as const,
    variables: {
      colorPrimary: "#1f2937",
      colorBackground: "#ffffff",
      colorText: "#1f2937",
      colorDanger: "#dc2626",
      fontFamily: "system-ui, sans-serif",
      spacingUnit: "6px",
      borderRadius: "8px",
      colorBorder: "#e5e7eb",
    },
  };

  // Determine which payment methods to show in the Payment Element
  const paymentMethodTypes = (() => {
    if (uiMode === "crypto") return ["crypto"];
    if (uiMode === "wallet") return []; // wallets are rendered via ExpressCheckoutElement only
    // Default card/paypal
    return ["card", "link", "paypal", "klarna"];
  })();

  // Elements configuration based on whether we have a client secret
  const elementsOptions = clientSecret
    ? {
        // Client secret mode (when paymentIntentId is provided)
        clientSecret: clientSecret,
        appearance,
        locale: "en" as const,
      }
    : {
        // Mode-based configuration (when no paymentIntentId)
        mode: "payment" as const,
        amount: amount,
        currency: currency.toLowerCase(), // Ensure currency is lowercase (e.g., "usd")
        // IMPORTANT: Set captureMethod to match the payment intent
        captureMethod: isEscrow ? ("manual" as const) : ("automatic" as const),
        // Only include payment methods that work with PaymentElement
        // Apple Pay and Google Pay are handled by ExpressCheckoutElement
        paymentMethodTypes,
        appearance,
        locale: "en" as const,
      };

  console.log("⚙️ Elements configuration:", {
    hasClientSecret: !!clientSecret,
    hasPaymentIntentId: !!paymentIntentId,
    isEscrow,
    captureMethod: isEscrow ? "manual" : "automatic",
    mode: clientSecret ? "client-secret" : "payment-mode",
  });

  return (
    <div className="min-h-screen  w-full grid grid-cols-2 gap-12 max-md:grid-cols-1 overflow-hidden px-2">
      {/* Left side - Product Details */}
      <div className="space-y-8">
        {/* Product Title */}
        <div>
          <h1 className="text-3xl font-bold text-gray-900 mb-2">{productName}</h1>
          <p className="text-gray-500">Complete your purchase securely</p>
        </div>

        <SimplePaymentServiceCard orderId={orderId || ""} />

        {/* Clean Order Summary */}
        {/* <div className="space-y-4">
          <h3 className="text-lg font-semibold text-gray-900">Order Summary</h3>
          <div className="space-y-3">
            <div className="flex justify-between items-center">
              <span className="text-gray-600">Order ID</span>
              <span className="font-mono text-sm text-gray-800">{orderId || "N/A"}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-gray-600">Total Amount</span>
              <span className="text-2xl font-bold text-gray-900">
                {currency.toUpperCase()} {(amount / 100).toFixed(2)}
              </span>
            </div>
          </div>
        </div> */}

        {/* Simple Security Note */}
        <div className="text-sm text-gray-500 flex items-center space-x-4">
          <span className="flex items-center">
            <svg className="w-4 h-4 text-green-500 mr-1" fill="currentColor" viewBox="0 0 20 20">
              <path
                fillRule="evenodd"
                d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z"
                clipRule="evenodd"
              />
            </svg>
            Secure Payment
          </span>
          <span>•</span>
          <span>SSL Encrypted</span>
        </div>
      </div>
      <Elements options={elementsOptions} stripe={stripePromise}>
        <CheckoutForm
          orderId={orderId}
          transactionId={transactionId}
          userId={userId}
          sellerId={sellerId}
          amount={amount}
          currency={currency}
          productName={productName}
          isEscrow={isEscrow}
          clientSecret={clientSecret}
          onSuccess={onSuccess}
          onError={onError}
        />
      </Elements>
    </div>
  );
}
