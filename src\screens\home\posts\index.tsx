// pages/ExamplePage.tsx
"use client";
import React from "react";
import ScrollButton from "@/components/bottomArrow";
import ScrollButtonRight from "@/components/rightArrow";
import { useCallback, useEffect, useRef, useState } from "react";
import { themes } from "../../../../theme";
import useAuth from "@/hook";
import CategoryComp from "@/globalComponents/homeComp/categoryComp";
import PostCompTest from "./test";
import PostMobileView from "./postMobileView";
import ScrollButtonLeft from "@/components/leftArrow";
import { useFilter } from "@/context/FilterContext";
import { MainContentFocus, PageSize, PostType, usePostsQuery } from "@/graphql/test/generated";
const data = [
  {
    img: "/assets/img.svg",
    imgMobile: "/assets/mobile-Img/myfeed.png",
    title: "My Feed",
    backgroundColor: "#000000",
  },
  {
    img: "/assets/music.svg",
    imgMobile: "/assets/mobile-Img/music.png",
    title: "Music",
    backgroundColor: "#E5B045",
  },
  {
    img: "/assets/litrature.svg",
    imgMobile: "/assets/mobile-Img/lit.png",
    title: "Literature",
    backgroundColor: "#CF5943",
  },
  {
    img: "/assets/art.svg",
    imgMobile: "/assets/mobile-Img/art.png",
    backgroundColor: "#3C5F9A",

    title: "Art",
  },
  {
    img: "/assets/film.svg",
    imgMobile: "/assets/mobile-Img/film.png",
    title: "Film & Photography",
    backgroundColor: "#46B933",
  },
  {
    img: "/assets/Theatre.svg",
    imgMobile: "/assets/mobile-Img/theater.png",
    title: "Theatre & Performance",
    backgroundColor: "#E073D2",
  },
  {
    img: "/assets/multi.svg",
    imgMobile: "/assets/mobile-Img/multi.png",
    title: "Multidisciplinary",
    backgroundColor: "#5331BC",
  },
  {
    img: "/assets/groups.svg",
    imgMobile: "/assets/mobile-Img/group.png",
    title: "Groups",
    backgroundColor: "#616770",
  },
];

const PostsHome = () => {
  const scrollRef = useRef<HTMLDivElement>(null);
  const user = useAuth(); // Assuming useAuth returns null or undefined if the user is not logged in
  const { filters } = useFilter();

  // Swipe Function
  const [touchStart, setTouchStart] = useState<any>(null);
  const [touchEnd, setTouchEnd] = useState<any>(null);

  // Track swipe state for each section
  const [swipedSections, setSwipedSections] = useState<boolean[]>(
    new Array(data.length).fill(false)
  );

  // the required distance between touchStart and touchEnd to be detected as a swipe
  const minSwipeDistance = 50;

  const onTouchStart = (e: any, index: any) => {
    setTouchEnd(null); // otherwise the swipe is fired even with usual touch events
    setTouchStart(e.targetTouches[0].clientX);
  };

  const onTouchMove = (e: any, index: any) => setTouchEnd(e.targetTouches[0].clientX);

  const onTouchEnd = (index: any) => {
    if (!touchStart || !touchEnd) return;
    const distance = touchStart - touchEnd;
    const isLeftSwipe = distance > minSwipeDistance;
    const isRightSwipe = distance < -minSwipeDistance;

    // Swipe left to go to the next section
    if (isLeftSwipe) {
      setSwipedSections((prev) => prev.map((item, idx) => (idx === index ? true : item)));
      // console.log("isLeft");
    }
    // Swipe right to go back to the previous section
    if (isRightSwipe) {
      setSwipedSections((prev) => prev.map((item, idx) => (idx === index ? false : item)));
      // console.log("isRight");
      // console.log();
    }
  };

  // State to manage the image height dynamically
  const [imgHeight, setImgHeight] = useState(150); // Initial height set to 350px
  const onScroll = useCallback((event: any) => {
    const { pageYOffset, scrollY } = window;
    // console.log("yOffset", pageYOffset, "scrollY", scrollY);
    // scrollY(window.pageYOffset);
  }, []);

  useEffect(() => {
    const handleScroll = () => {
      // Reduce image height based on scroll, with a minimum of 100px
      const newHeight = Math.max(100, 150 - window.scrollY);
      setImgHeight(newHeight);
      // console.log(imgHeight);
    };

    window.removeEventListener("scroll", onScroll);
    window.addEventListener("scroll", handleScroll);

    return () => {
      window.removeEventListener("scroll", onScroll);

      window.removeEventListener("scroll", handleScroll);
    };
  }, []);

  function hexToRGBA(hex: string, alpha: number): string {
    const rgb = hex
      .replace("#", "")
      .match(/.{1,2}/g)
      ?.map((x) => parseInt(x, 16));
    return rgb ? `rgba(${rgb[0]}, ${rgb[1]}, ${rgb[2]}, ${alpha})` : hex;
  }

  // fetch the lens data
  // States for profile data
  const [profileData, setProfileData] = useState<any>(null);
  const [loadingProfile, setLoadingProfile] = useState(true);

  // States for publications data
  const [publicationsData, setPublicationsData] = useState<any>(null);
  const [loadingPublications, setLoadingPublications] = useState(true);
  const [publicationsError, setPublicationsError] = useState<any | null>(null);

  const {
    isLoading: isLoadingPublications,
    data: publicationsQueryData,
    error: publicationsQueryError,
  } = usePostsQuery(
    {
      request: {
        filter: {
          authors: profileData?.profile?.id, // joanakawaharalino
          postTypes: [PostType.Root],
          metadata: {
            mainContentFocus: [MainContentFocus.Image, MainContentFocus.Video],
          },
        },
        // cursor:null
        pageSize: PageSize.Fifty,
      },
    },
    {
      refetchOnWindowFocus: false,
      enabled: !!profileData?.profile?.id, // Only execute query when profile ID exists
    }
  );

  // Update publications states when publications data changes
  useEffect(() => {
    setLoadingPublications(isLoadingPublications);
    if (publicationsQueryData) {
      setPublicationsData(publicationsQueryData);
    }
    if (publicationsQueryError) {
      setPublicationsError(publicationsQueryError);
    }
  }, [isLoadingPublications, publicationsQueryData, publicationsQueryError]);

  return (
    <div className="relative p-0">
      <div className="max-md:hidden">
        <ScrollButton scrollRef={scrollRef} />
        <ScrollButtonRight scrollRef={scrollRef} />
        <ScrollButtonLeft scrollRef={scrollRef} />
      </div>

      <div
        ref={scrollRef}
        className="overflow-y-auto p-0 md:h-[calc(100vh-205px)] max-md:h-screen hide-scroll"
        // style={{ maxHeight: "100vh" }}
      >
        <div
          className={`space-y-0 flex flex-row max-md:flex-col w-full max-md:gap-4 gap-3 ${
            user.isLogin ? "pl-2" : ""
          }`}
        >
          {(() => {
            const filteredData = data.filter((item) => {
              // Apply category filtering
              if (filters.categories && filters.categories.length > 0) {
                return filters.categories.includes(item.title);
              }
              return true; // Show all categories if no filter is applied
            });

            // Show message when no categories match the filter
            if (filters.categories && filters.categories.length > 0 && filteredData.length === 0) {
              return (
                <div className="w-full flex items-center justify-center min-h-[400px]">
                  <div className="text-center p-8 bg-gray-50 rounded-lg border border-gray-200 max-w-md">
                    <div className="mb-4">
                      <svg
                        className="w-16 h-16 text-gray-400 mx-auto"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth="2"
                          d="M9.172 16.172a4 4 0 015.656 0M9 12h6m-6-4h6m2 5.291A7.962 7.962 0 0112 15c-2.34 0-4.467-.881-6.08-2.33"
                        />
                      </svg>
                    </div>
                    <h3 className="text-lg font-medium text-gray-900 mb-2">
                      No matching categories
                    </h3>
                    <p className="text-gray-500">
                      No posts found for the selected categories. Try adjusting your filter
                      settings.
                    </p>
                  </div>
                </div>
              );
            }

            return filteredData.map((item, index) => {
              return (
                <div
                  key={`${item.title}-${filters.categories?.join(",") || "all"}`}
                  ref={scrollRef}
                >
                  {!swipedSections[index] ? (
                    <div
                      className=""
                      key={index}
                      onTouchStart={(e) => onTouchStart(e, index)}
                      onTouchMove={(e) => onTouchMove(e, index)}
                      onTouchEnd={() => onTouchEnd(index)}
                    >
                      {!user.isLogin && !(item.title == "My Feed") && (
                        <>
                          <CategoryComp item={item} />
                        </>
                      )}

                      {user.isLogin && (
                        <>
                          <CategoryComp item={item} />
                        </>
                      )}

                      {/* {!user.isLogin && !(item.title == "My Feed") && ( */}
                      <div className="md:overflow-scroll hide-scroll  md:h-full">
                        <div className="max-md:hidden md:overflow-hidden">
                          {Array.from({ length: 1 }).map((_, indexobj) => (
                            <div key={indexobj}>
                              {Object.entries(themes).map(
                                ([themeName, themeProperties]) =>
                                  themeProperties.title === item.title && (
                                    <div key={themeName}>
                                      {!user.isLogin && !(item.title == "My Feed") && (
                                        <>
                                          <PostCompTest
                                            themeProperties={themeProperties}
                                            isloading={loadingPublications && loadingProfile}
                                          />
                                        </>
                                      )}

                                      {user.isLogin && (
                                        <>
                                          {/* <PostComp
                                          themeProperties={themeProperties}
                                        /> */}
                                          <PostCompTest
                                            themeProperties={themeProperties}
                                            isloading={loadingPublications && loadingProfile}
                                          />
                                        </>
                                      )}
                                    </div>
                                  )
                              )}
                            </div>
                          ))}
                        </div>
                        {/* for mobile view */}
                        <div
                          className="md:hidden row overflow-x-hidden w-full bg-green-800 pt-0 -mt-[2px]"
                          style={{
                            backgroundColor: item.backgroundColor.startsWith("#")
                              ? hexToRGBA(item.backgroundColor, 0.7)
                              : item.backgroundColor, // Use the original if already in rgba or other format
                          }}
                        >
                          <div className="">
                            {Object.entries(themes).map(
                              ([themeName, themeProperties]) =>
                                themeProperties.title === item.title && (
                                  <div key={themeName} className="">
                                    {!user.isLogin && !(item.title == "My Feed") && (
                                      <div className="">
                                        <PostMobileView
                                          key={`mobile-${themeProperties.title}-${filters.categories?.join(",") || "all"}`}
                                          themeProperties={themeProperties}
                                          borderColor={themeProperties.backgroundColor}
                                        />
                                        {/* <p>{themeProperties.title}</p> */}
                                      </div>
                                    )}
                                    {user.isLogin && (
                                      <div className="w-full py-0 pl-0">
                                        <div className="">
                                          <PostMobileView
                                            key={`mobile-logged-${themeProperties.title}-${filters.categories?.join(",") || "all"}`}
                                            themeProperties={themeProperties}
                                            borderColor={themeProperties.backgroundColor}
                                          />
                                        </div>
                                      </div>
                                    )}
                                  </div>
                                )
                            )}
                          </div>
                        </div>
                      </div>
                    </div>
                  ) : (
                    <div className="">
                      {!user.isLogin && !(item.title == "My Feed") && (
                        <div
                          key={index}
                          onTouchStart={(e) => onTouchStart(e, index)}
                          onTouchMove={(e) => onTouchMove(e, index)}
                          onTouchEnd={() => onTouchEnd(index)}
                          className=""
                        >
                          <div
                            className="w-full rounded-tl-lg md:hidden -mb-[1px]"
                            style={{
                              backgroundColor: item.backgroundColor.startsWith("#")
                                ? hexToRGBA(item.backgroundColor, 0.7)
                                : item.backgroundColor, // Use the original if already in rgba or other format
                            }}
                          >
                            <p className="pl-4 text-lg font-bold text-white ">{item.title}</p>
                          </div>

                          <div
                            className="md:hidden row overflow-x-scroll w-full "
                            style={{
                              backgroundColor: item.backgroundColor.startsWith("#")
                                ? hexToRGBA(item.backgroundColor, 0.7)
                                : item.backgroundColor, // Use the original if already in rgba or other format
                            }}
                          >
                            {Array.from({ length: 1 }).map((_, indexs) => (
                              <div key={indexs} className="row gap-0 mr-[0px]">
                                {Object.entries(themes).map(
                                  ([themeName, themeProperties]) =>
                                    themeProperties.title === item.title && (
                                      <div className="w-full" key={indexs}>
                                        <PostMobileView
                                          key={`mobile-scroll-${themeProperties.title}-${filters.categories?.join(",") || "all"}`}
                                          themeProperties={themeProperties}
                                          isScroll={true}
                                          borderColor={themeProperties.backgroundColor}
                                        />
                                      </div>
                                    )
                                )}
                              </div>
                            ))}
                          </div>
                        </div>
                      )}
                      {user.isLogin && (
                        <div
                          key={index}
                          onTouchStart={(e) => onTouchStart(e, index)}
                          onTouchMove={(e) => onTouchMove(e, index)}
                          onTouchEnd={() => onTouchEnd(index)}
                          className=""
                        >
                          <div
                            className="w-full rounded-tl-lg md:hidden -mb-[1px]"
                            style={{
                              backgroundColor: item.backgroundColor.startsWith("#")
                                ? hexToRGBA(item.backgroundColor, 0.7)
                                : item.backgroundColor, // Use the original if already in rgba or other format
                            }}
                          >
                            <p className="pl-4 text-lg font-bold text-white ">{item.title}</p>
                          </div>

                          <div
                            className="md:hidden row overflow-x-scroll "
                            style={{
                              backgroundColor: item.backgroundColor.startsWith("#")
                                ? hexToRGBA(item.backgroundColor, 0.7)
                                : item.backgroundColor, // Use the original if already in rgba or other format
                            }}
                          >
                            {Array.from({ length: 1 }).map((_, indexs) => (
                              <div key={indexs} className="row gap-2">
                                {Object.entries(themes).map(
                                  ([themeName, themeProperties]) =>
                                    themeProperties.title === item.title && (
                                      <div className="w-full " key={indexs}>
                                        <div className="">
                                          <PostMobileView
                                            key={`mobile-scroll2-${themeProperties.title}-${filters.categories?.join(",") || "all"}`}
                                            themeProperties={themeProperties}
                                            isScroll={true}
                                            borderColor={themeProperties.backgroundColor}
                                          />
                                        </div>
                                      </div>
                                    )
                                )}
                              </div>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                  )}
                </div>
              );
            });
          })()}
        </div>
      </div>
    </div>
  );
};

export default PostsHome;
