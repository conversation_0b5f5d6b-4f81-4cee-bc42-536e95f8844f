import { useState, useEffect, JS<PERSON>, SVGProps } from "react";
import { GlobalCard } from "@/globalComponents/globalCard";
import { Edit2, X, Check, Loader } from "react-feather";
import useAuth from "@/hook";
import useProfile from "@/hook/profileData";
import { getServiceById, getServicesByUserId } from "@/services/serviceService";
import { themes } from "../../../../../../../theme";

import { Divider, Modal, ModalBody, ModalContent, ModalHeader } from "@heroui/react";
import { getCurrencySymbol, updateCurrency, initializeCurrency } from "@/services/currencyService";
import { updateUser } from "@/services/usersServices";
import { Button } from "@/components/ui/button";
import CardSkeleton from "@/components/CardSkeleton";
import EmptyState from "@/components/EmptyState";

// Available currencies for selection
export const currencies = [
  { code: "USD", symbol: "$", name: "US Dollar" },
  { code: "EUR", symbol: "€", name: "Euro" },
  { code: "GBP", symbol: "£", name: "British Pound" },
];

const ServicesCard = ({ onSelectService, activeColor, setParentIsOpen }: any) => {
  const auth = useAuth();
  const profile = useProfile(auth?.userData?.uid);

  // State to store fetched services
  const [services, setServices] = useState<any[]>([]);
  const [servicesdan, setServicesdan]: any = useState([]);
  const [isOpen, setIsOpen] = useState(false);
  const [isConfirm, setIsConfirm] = useState(false);
  const [loading, setLoading] = useState(true); // Add loading state
  const [isSaving, setIsSaving] = useState(false); // Add saving state
  const [saveSuccess, setSaveSuccess] = useState(false); // Add success state
  const [refreshTrigger, setRefreshTrigger] = useState(0); // Add refresh trigger

  const [currencySymbol, setCurrencySymbol] = useState("");

  // Get user services from profile
  useEffect(() => {
    const fetchAllServices = async () => {
      if (profile?.profileData?.services) {
        console.log({profile});
        
        const fetchedServices = await Promise.all(
          profile.profileData.services.map(async (serviceId: string) => {
            const response = await getServiceById(serviceId);
            return response?.success ? { ...response.service, serviceId } : null;
          })
        );
        setServices(fetchedServices.filter((service) => service !== null)); // Filter out invalid services
      }
    };

    fetchAllServices();
  }, [profile?.profileData?.services]);

  // Get services directly by user ID
  useEffect(() => {
    const fetchAllServices = async () => {
      setLoading(true); // Set loading to true when fetching starts
      if (auth?.userData?.uid) {
        const responsedan = await getServicesByUserId(auth?.userData?.uid);

        // Ensure all services have a consistent currency if not set
        if (responsedan.success && responsedan.services) {
          // Get the current currency from localStorage or profile
          const currentCurrency = profile?.profileData?.currency || initializeCurrency();

          // Update the currency symbol in the UI
          setCurrencySymbol(getCurrencySymbol(currentCurrency));

          const servicesWithCurrency = responsedan.services.map((service: any) => {
            if (!service.currency) {
              return {
                ...service,
                currency: currentCurrency,
              };
            }
            return service;
          });

          // Sort services by created_at timestamp (most recent first)
          const sortedServices = servicesWithCurrency.sort((a: any, b: any) => {
            // Handle Firebase timestamp objects
            const getTimestamp = (service: any) => {
              if (service.created_at) {
                // If it's a Firebase timestamp object with toDate() method
                if (typeof service.created_at.toDate === "function") {
                  return service.created_at.toDate().getTime();
                }
                // If it's already a date string or timestamp
                return new Date(service.created_at).getTime();
              }
              return 0; // Default for services without created_at
            };

            const timestampA = getTimestamp(a);
            const timestampB = getTimestamp(b);

            return timestampB - timestampA; // Descending order (newest first)
          });

          setServicesdan(sortedServices);
        } else {
          setServicesdan([]);
        }
      }
      setLoading(false); // Set loading to false when fetching completes
    };

    fetchAllServices();
  }, [auth?.userData?.uid, refreshTrigger, profile?.profileData?.currency]);

  // Get currency symbol when component mounts or profile changes
  useEffect(() => {
    try {
      // Get current currency and set the symbol
      setCurrencySymbol(getCurrencySymbol());

      // Set initial selected currency from profile or default
      if (profile?.profileData?.currency) {
        setSelectedCurrency(profile.profileData.currency.trim().toUpperCase());
      } else {
        setSelectedCurrency(initializeCurrency());
      }
    } catch (error) {
      console.error("Error getting currency symbol:", error);
      setCurrencySymbol("£"); // Default fallback
    }
  }, [profile?.profileData]);

  // Get the appropriate background color for a category
  // This function is now handled directly in the JSX

  const [selectedCurrency, setSelectedCurrency] = useState(
    profile?.profileData?.currency || initializeCurrency()
  );

  const handleCurrencySelect = (currencyCode: string) => {
    setSelectedCurrency(currencyCode);
  };

  // When the modal opens, set the selected currency from profile or localStorage
  useEffect(() => {
    if (isOpen) {
      try {
        // Try to get from profile first
        if (profile?.profileData?.currency) {
          const currencyFromProfile = profile.profileData.currency.trim().toUpperCase();
          setSelectedCurrency(currencyFromProfile);
        } else {
          // Otherwise get from localStorage via initializeCurrency
          const currentCurrency = initializeCurrency();
          setSelectedCurrency(currentCurrency);
        }
      } catch (error) {
        console.error("Error setting currency in modal:", error);
        setSelectedCurrency("GBP"); // Default fallback
      }
    }
  }, [isOpen, profile?.profileData?.currency]);

  const handleSubmit = async () => {
    if (selectedCurrency) {
      try {
        // Set loading state
        setIsSaving(true);
        setSaveSuccess(false);

        // 1. Update in user profile
        const updatedData = {
          currency: selectedCurrency,
        };
        const response = await updateUser(auth?.userData?.uid, updatedData);

        if (response.success) {
          // 2. Update in localStorage using the enhanced service
          updateCurrency(selectedCurrency);

          // 3. Update the current symbol in the component
          setCurrencySymbol(getCurrencySymbol(selectedCurrency));

          // Set success state
          setSaveSuccess(true);

          // Trigger a refresh of the services data
          setRefreshTrigger((prev) => prev + 1);

          // Auto close after success
          setTimeout(() => {
            setSaveSuccess(false);
            setIsConfirm(false);
          }, 1500);
        } else {
          console.error("Failed to update user currency:", response.error);
          setSaveSuccess(false);
        }
      } catch (error) {
        console.error("Error updating currency:", error);
        setSaveSuccess(false);
      } finally {
        // Always reset loading state
        setTimeout(() => {
          setIsSaving(false);
        }, 500);
      }
    }
  };

  return (
    <>
      <div>
        <div className="w-full bg-white sticky top-[7.2rem] max-md:top-[5.6rem] z-50">
          <div className="row justify-between py-2">
            <p className="text-primary text-xl max-md:text-base font-bold">My Services</p>
            <div className="row gap-1 cursor-pointer" onClick={() => setIsOpen(true)}>
              <div className="text-primary text-xl font-bold px-2 max-md:text-base">
                {currencySymbol || "£"}
              </div>
              <Edit2 color={activeColor} className="cursor-pointer h-4" strokeWidth="2px" />
            </div>
          </div>
        </div>
        <div className="bg-white pt-2">
          {loading ? (
            <div className="grid grid-cols-1 max-md:grid-cols-1 max-lg:grid-cols-1 gap-3">
              <CardSkeleton count={3} columns={1} showGrid={true} />
            </div>
          ) : (
            <div className="grid grid-cols-1 max-md:grid-cols-1 max-lg:grid-cols-1 gap-3">
              {servicesdan.length > 0 ? (
                servicesdan.map((service: any, index: any) => (
                  <div
                    className="cursor-pointer"
                    key={index}
                    onClick={() => onSelectService(service.id)}
                  >
                    {/* <GlobalCard
                      border={activeColor}
                      title={service.title}
                      description={service.description}
                      price={service.price}
                      duration={service.duration}
                      currency={service.currency}
                    /> */}
                    {Object.entries(themes).map(([_, innerThemeProperties]) => (
                      <div key={innerThemeProperties.title}>
                        {(service.category === "Storytelling" ? "Literature" : service.category) ===
                          innerThemeProperties.title && (
                          <GlobalCard
                            border={innerThemeProperties.backgroundColor}
                            title={service.title}
                            description={service.description}
                            price={service.price}
                            duration={service?.duration}
                            currency={
                              service?.currency ||
                              profile?.profileData?.currency ||
                              initializeCurrency()
                            }
                          />
                        )}
                      </div>
                    ))}
                  </div>
                ))
              ) : (
                <EmptyState
                  type="services"
                  title="No Services Yet"
                  message="Create your first service to start offering your skills"
                  isOwnProfile={true}
                  actionLabel="Create Service"
                  onAction={() => {
                    if (setParentIsOpen) {
                      setParentIsOpen(true);
                    } else {
                      onSelectService("new");
                    }
                  }}
                />
              )}
            </div>
          )}
        </div>
        <div>
          <Modal
            isDismissable={false}
            isOpen={isOpen}
            placement="auto"
            onOpenChange={setIsOpen}
            hideCloseButton={true}
          >
            <ModalContent className="modal-content">
              {() => (
                <>
                  <ModalHeader>
                    <div className="row justify-between w-full">
                      <div
                        onClick={() => {
                          setIsOpen(false);
                        }}
                        className=" cursor-pointer"
                      >
                        <X />
                      </div>
                      <button
                        className={
                          selectedCurrency
                            ? "font-bold text-primary cursor-pointer border-none bg-transparent flex items-center"
                            : "font-bold text-borderColor cursor-not-allowed border-none bg-transparent flex items-center"
                        }
                        onClick={() => {
                          if (selectedCurrency) {
                            setIsConfirm(true);
                            setIsOpen(false);
                          }
                        }}
                        disabled={!selectedCurrency}
                      >
                        Save
                      </button>
                    </div>
                  </ModalHeader>
                  <Divider />
                  <ModalBody>
                    <div className="w-full max-w-md mx-auto p-4 px-4 pb-8">
                      <div className="flex flex-col gap-3">
                        {currencies.map((currency) => (
                          <button
                            key={currency.code}
                            onClick={() => handleCurrencySelect(currency.code)}
                            className={`flex items-center justify-start px-4 py-3 rounded-lg border transition-all duration-200 w-full ${
                              selectedCurrency === currency.code
                                ? `bg-primary text-white border-primary font-medium`
                                : "bg-white text-gray-800 border-gray-300"
                            }`}
                          >
                            <span className="mr-3 text-lg">{currency.symbol}</span>
                            <span>{currency.code}</span>
                            {selectedCurrency === currency.code && (
                              <span className="ml-auto">
                                <svg
                                  xmlns="http://www.w3.org/2000/svg"
                                  className="h-5 w-5"
                                  viewBox="0 0 20 20"
                                  fill="currentColor"
                                >
                                  <path
                                    fillRule="evenodd"
                                    d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                                    clipRule="evenodd"
                                  />
                                </svg>
                              </span>
                            )}
                          </button>
                        ))}
                      </div>
                    </div>
                  </ModalBody>
                </>
              )}
            </ModalContent>
          </Modal>
          <div>
            <Modal
              isDismissable={false}
              isOpen={isConfirm}
              placement="auto"
              onOpenChange={(open) => {
                setIsConfirm(open);
                if (!open) {
                  // Reset states when modal is closed
                  setIsSaving(false);
                  setSaveSuccess(false);
                }
              }}
              hideCloseButton={true}
            >
              <ModalContent className="modal-content">
                {() => (
                  <>
                    <ModalBody>
                      <div>
                        <p className="text-center text-black text-lg">
                          Please double check your desired service prices after changing currencies.
                          Note that when changing currencies, the original service price amounts are
                          not automatically converted to reflect the new currency type selected. You
                          may edit or convert your service prices manually.
                        </p>
                        <div>
                          {saveSuccess ? (
                            <div className="flex flex-col items-center justify-center py-4">
                              <div className="bg-green-50 rounded-full p-4 mb-4">
                                <Check size={32} className="text-green-500" />
                              </div>
                              <p className="text-green-600 font-medium text-lg">
                                Currency updated successfully!
                              </p>
                            </div>
                          ) : (
                            <>
                              <Button
                                variant="outline"
                                className="rounded-full w-full mt-5 border-black text-black border-2 py-5 text-base relative"
                                onClick={() => {
                                  handleSubmit();
                                }}
                                disabled={isSaving}
                              >
                                {isSaving ? (
                                  <div className="flex items-center justify-center">
                                    <Loader size={20} className="animate-spin mr-2" />
                                    <span>Saving...</span>
                                  </div>
                                ) : (
                                  "OK"
                                )}
                              </Button>
                              <Button
                                variant="outline"
                                className="rounded-full w-full mt-3 border-black text-black border-2 py-5 text-base"
                                onClick={() => setIsConfirm(false)}
                                disabled={isSaving}
                              >
                                No, cancel
                              </Button>
                            </>
                          )}
                        </div>
                      </div>
                    </ModalBody>
                  </>
                )}
              </ModalContent>
            </Modal>
          </div>
        </div>
      </div>
    </>
  );
};

export default ServicesCard;
