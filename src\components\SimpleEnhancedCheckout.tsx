// "use client";

// import React, { useEffect, useState } from "react";
// import {
//   useStripe,
//   useElements,
//   PaymentElement,
//   PaymentRequestButtonElement,
// } from "@stripe/react-stripe-js";
// import { useCurrentUser } from "@/hooks/useCurrentUser";

// interface SimpleEnhancedCheckoutProps {
//   amount: number;
//   currency?: string;
//   productName?: string;
//   userId?: string;
//   sellerId?: string;
//   orderId?: string;
//   isEscrow?: boolean;
//   onSuccess?: (paymentIntent: any) => void;
//   onError?: (error: string) => void;
//   enableApplePay?: boolean;
//   enableGooglePay?: boolean;
// }

// // Simple currency detection from localStorage
// const getCurrencyFromStorage = (): string => {
//   try {
//     return localStorage.getItem('currency') || 'usd';
//   } catch {
//     return 'usd';
//   }
// };

// // Simple user data extraction from localStorage
// const getUserFromStorage = () => {
//   try {
//     const userStr = localStorage.getItem('user');
//     return userStr ? JSON.parse(userStr) : null;
//   } catch {
//     return null;
//   }
// };

// const SimpleEnhancedCheckout = ({ 
//   amount, 
//   currency,
//   productName = "Product",
//   userId,
//   sellerId,
//   orderId,
//   isEscrow = false,
//   onSuccess,
//   onError,
//   enableApplePay = true,
//   enableGooglePay = true
// }: SimpleEnhancedCheckoutProps) => {
//   const stripe = useStripe();
//   const elements = useElements();
//   const { user } = useCurrentUser();
  
//   const [errorMessage, setErrorMessage] = useState<string>();
//   const [clientSecret, setClientSecret] = useState("");
//   const [loading, setLoading] = useState(false);
//   const [paymentRequest, setPaymentRequest] = useState<any>(null);
//   const [canMakePayment, setCanMakePayment] = useState(false);

//   // Get currency - use provided currency or detect from localStorage
//   const finalCurrency = currency || getCurrencyFromStorage();
  
//   // Get user data
//   const userFromStorage = getUserFromStorage();
//   const finalUser = user || userFromStorage;

//   // Initialize Payment Request (Apple Pay/Google Pay)
//   useEffect(() => {
//     if (!stripe || !enableApplePay && !enableGooglePay) return;

//     const pr = stripe.paymentRequest({
//       country: 'US',
//       currency: finalCurrency.toLowerCase(),
//       total: {
//         label: productName,
//         amount: amount,
//       },
//       requestPayerName: true,
//       requestPayerEmail: true,
//     });

//     // Check if Payment Request is available
//     pr.canMakePayment().then((result: any) => {
//       if (result) {
//         setPaymentRequest(pr);
//         setCanMakePayment(true);
//       }
//     });

//     pr.on('paymentmethod', async (ev: any) => {
//       try {
//         // Create payment intent for Payment Request
//         const response = await fetch(isEscrow ? "/api/escrow/create-payment-intent" : "/api/create-payment-intent", {
//           method: "POST",
//           headers: {
//             "Content-Type": "application/json",
//           },
//           body: JSON.stringify({ 
//             amount,
//             currency: finalCurrency,
//             productName,
//             userId: userId || finalUser?.uid,
//             sellerId,
//             orderId,
//             isEscrow,
//             userEmail: finalUser?.email,
//             userName: finalUser?.displayName
//           }),
//         });
        
//         const { clientSecret: newClientSecret, error } = await response.json();
        
//         if (error) {
//           ev.complete('fail');
//           onError?.(error);
//           return;
//         }

//         // Confirm payment
//         const { error: confirmError, paymentIntent } = await stripe.confirmCardPayment(
//           newClientSecret,
//           { payment_method: ev.paymentMethod.id },
//           { handleActions: false }
//         );

//         if (confirmError) {
//           ev.complete('fail');
//           onError?.(confirmError.message || "Payment failed");
//         } else {
//           ev.complete('success');
//           if (paymentIntent && paymentIntent.status === "succeeded") {
//             onSuccess?.(paymentIntent);
//           }
//         }
//       } catch (error) {
//         ev.complete('fail');
//         onError?.(error instanceof Error ? error.message : "Payment failed");
//       }
//     });
//   }, [stripe, finalCurrency, amount, productName, userId, sellerId, orderId, isEscrow, finalUser, onSuccess, onError, enableApplePay, enableGooglePay]);

//   // Create Payment Intent for regular card payments
//   useEffect(() => {
//     const createPaymentIntent = async () => {
//       try {
//         const endpoint = isEscrow ? "/api/escrow/create-payment-intent" : "/api/create-payment-intent";
//         const response = await fetch(endpoint, {
//           method: "POST",
//           headers: {
//             "Content-Type": "application/json",
//           },
//           body: JSON.stringify({ 
//             amount,
//             currency: finalCurrency,
//             productName,
//             userId: userId || finalUser?.uid,
//             sellerId,
//             orderId,
//             isEscrow,
//             userEmail: finalUser?.email,
//             userName: finalUser?.displayName
//           }),
//         });
        
//         const data = await response.json();
//         if (data.clientSecret) {
//           setClientSecret(data.clientSecret);
//         } else {
//           throw new Error(data.error || "Failed to create payment intent");
//         }
//       } catch (error) {
//         const message = error instanceof Error ? error.message : "Unknown error";
//         setErrorMessage(message);
//         onError?.(message);
//       }
//     };

//     createPaymentIntent();
//   }, [amount, finalCurrency, productName, userId, sellerId, orderId, isEscrow, finalUser]);

//   const handleSubmit = async (event: React.FormEvent<HTMLFormElement>) => {
//     event.preventDefault();
//     setLoading(true);

//     if (!stripe || !elements) {
//       return;
//     }

//     const { error: submitError } = await elements.submit();

//     if (submitError) {
//       setErrorMessage(submitError.message);
//       setLoading(false);
//       return;
//     }

//     const { error, paymentIntent } = await stripe.confirmPayment({
//       elements,
//       clientSecret,
//       confirmParams: {
//         return_url: `${window.location.origin}/payment-success?amount=${amount}&currency=${finalCurrency}${orderId ? `&order_id=${orderId}` : ''}`,
//         payment_method_data: {
//           billing_details: {
//             name: finalUser?.displayName,
//             email: finalUser?.email,
//           },
//         },
//       },
//       redirect: "never"
//     });

//     if (error) {
//       setErrorMessage(error.message);
//       onError?.(error.message || "Payment failed");
//     } else if (paymentIntent && paymentIntent.status === "succeeded") {
//       onSuccess?.(paymentIntent);
//     }

//     setLoading(false);
//   // }

//   if (!clientSecret || !stripe || !elements) {
//     return (
//       <div className="flex items-center justify-center">
//         <div
//           className="inline-block h-8 w-8 animate-spin rounded-full border-4 border-solid border-current border-e-transparent align-[-0.125em] text-surface motion-reduce:animate-[spin_1.5s_linear_infinite] dark:text-white"
//           role="status"
//         >
//           <span className="!absolute !-m-px !h-px !w-px !overflow-hidden !whitespace-nowrap !border-0 !p-0 ![clip:rect(0,0,0,0)]">
//             Loading...
//           </span>
//         </div>
//       </div>
//     );
//   }

//   return (
//     <div className="bg-white p-2 rounded-md">
//       {/* Payment Request Button (Apple Pay/Google Pay) */}
//       {canMakePayment && paymentRequest && (enableApplePay || enableGooglePay) && (
//         <div className="mb-4">
//           <PaymentRequestButtonElement 
//             options={{ paymentRequest }}
//             className="PaymentRequestButton"
//           />
//           <div className="flex items-center my-4">
//             <div className="flex-1 border-t border-gray-300"></div>
//             <span className="px-3 text-gray-500 text-sm">or pay with card</span>
//             <div className="flex-1 border-t border-gray-300"></div>
//           </div>
//         </div>
//       )}

//       {/* Regular Payment Form */}
//       <form onSubmit={handleSubmit}>
//         {clientSecret && <PaymentElement />}

//         {errorMessage && (
//           <div className="mt-2 p-2 bg-red-100 border border-red-400 text-red-700 rounded text-sm">
//             {errorMessage}
//           </div>
//         )}

//         <button
//           disabled={!stripe || loading}
//           className="text-white w-full p-5 bg-black mt-2 rounded-md font-bold disabled:opacity-50 disabled:animate-pulse"
//         >
//           {!loading ? `Pay ${finalCurrency.toUpperCase()} ${(amount / 100).toFixed(2)}` : "Processing..."}
//         </button>
//       </form>

//       {/* Simple payment info */}
//       <div className="mt-2 text-xs text-gray-600">
//         {finalUser?.email && <p>Receipt will be sent to: {finalUser.email}</p>}
//         {isEscrow && <p className="text-blue-600">🔒 Escrow Payment</p>}
//       </div>
//     </div>
//   );
// };

// export default SimpleEnhancedCheckout;
