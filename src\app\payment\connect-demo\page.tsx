"use client";

import React, { useState } from 'react';
import { ConnectEmbeddedProvider } from '@/components/ConnectEmbeddedProvider';
import ConnectAccountOnboardingWrapper from '@/components/ConnectAccountOnboarding';
import ConnectAccountManagementWrapper, { ConnectPayoutsWrapper } from '@/components/ConnectAccountManagement';
import ConnectPaymentsWrapper from '@/components/ConnectPayments';
import ConnectEnhancedPaymentForm from '@/components/ConnectEnhancedPaymentForm';
import { useCurrentUser } from '@/hooks/useCurrentUser';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { 
  CreditCard, 
  Settings, 
  DollarSign, 
  User, 
  ShoppingCart,
  Building,
  Loader2 
} from 'lucide-react';

export default function ConnectDemoPage() {
  const { user, loading: userLoading } = useCurrentUser();
  const [demoAccountId, setDemoAccountId] = useState('acct_1QSJJhRqnAbyhXpf'); // Demo account ID
  const [paymentAmount, setPaymentAmount] = useState(2000); // $20.00
  const [activeTab, setActiveTab] = useState('onboarding');

  if (userLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
          <p className="text-gray-600">Loading...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto px-4 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Stripe Connect Embedded Components Demo
          </h1>
          <p className="text-gray-600 mb-6">
            Experience the new Stripe Connect embedded components that provide a seamless 
            integration experience without redirects.
          </p>

          {/* Demo Configuration */}
          <Card className="mb-6">
            <CardHeader>
              <CardTitle className="flex items-center">
                <Settings className="h-5 w-5 mr-2" />
                Demo Configuration
              </CardTitle>
              <CardDescription>
                Configure the demo settings to test different scenarios
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="accountId">Demo Account ID</Label>
                  <Input
                    id="accountId"
                    value={demoAccountId}
                    onChange={(e) => setDemoAccountId(e.target.value)}
                    placeholder="acct_..."
                  />
                  <p className="text-xs text-gray-500">
                    Use your own Connect account ID or the demo account
                  </p>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="amount">Payment Amount (cents)</Label>
                  <Input
                    id="amount"
                    type="number"
                    value={paymentAmount}
                    onChange={(e) => setPaymentAmount(parseInt(e.target.value) || 0)}
                    placeholder="2000"
                  />
                  <p className="text-xs text-gray-500">
                    Amount in cents (e.g., 2000 = $20.00)
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        <ConnectEmbeddedProvider 
          accountId={demoAccountId}
          components={[
            'account_onboarding', 
            'account_management', 
            'payments', 
            'payouts', 
            'balances',
            'notification_banner'
          ]}
        >
          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
            <TabsList className="grid w-full grid-cols-5">
              <TabsTrigger value="onboarding" className="flex items-center">
                <User className="h-4 w-4 mr-2" />
                Onboarding
              </TabsTrigger>
              <TabsTrigger value="management" className="flex items-center">
                <Building className="h-4 w-4 mr-2" />
                Management
              </TabsTrigger>
              <TabsTrigger value="payments" className="flex items-center">
                <CreditCard className="h-4 w-4 mr-2" />
                Payments
              </TabsTrigger>
              <TabsTrigger value="payouts" className="flex items-center">
                <DollarSign className="h-4 w-4 mr-2" />
                Payouts
              </TabsTrigger>
              <TabsTrigger value="checkout" className="flex items-center">
                <ShoppingCart className="h-4 w-4 mr-2" />
                Checkout
              </TabsTrigger>
            </TabsList>

            <TabsContent value="onboarding" className="mt-6">
              <Card>
                <CardHeader>
                  <CardTitle>Account Onboarding</CardTitle>
                  <CardDescription>
                    Embedded onboarding component that allows users to complete their 
                    Stripe account setup without leaving your application.
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <ConnectAccountOnboardingWrapper
                    onOnboardingComplete={(accountId) => {
                      console.log('Onboarding completed for account:', accountId);
                    }}
                  />
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="management" className="mt-6">
              <Card>
                <CardHeader>
                  <CardTitle>Account Management</CardTitle>
                  <CardDescription>
                    Embedded account management component for updating business information,
                    bank details, and other account settings.
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <ConnectAccountManagementWrapper
                    accountId={demoAccountId}
                    showNotificationBanner={true}
                  />
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="payments" className="mt-6">
              <Card>
                <CardHeader>
                  <CardTitle>Payment Management</CardTitle>
                  <CardDescription>
                    Embedded payments component for viewing transactions, managing refunds,
                    and handling disputes.
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <ConnectPaymentsWrapper
                    accountId={demoAccountId}
                    onPaymentSelect={(paymentId) => {
                      console.log('Payment selected:', paymentId);
                    }}
                  />
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="payouts" className="mt-6">
              <Card>
                <CardHeader>
                  <CardTitle>Payouts & Balance</CardTitle>
                  <CardDescription>
                    Embedded payouts component for viewing balance and managing 
                    payout schedules and destinations.
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <ConnectPayoutsWrapper
                    accountId={demoAccountId}
                  />
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="checkout" className="mt-6">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Standard Payment</CardTitle>
                    <CardDescription>
                      Regular Stripe payment form without Connect
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <ConnectEnhancedPaymentForm
                      amount={paymentAmount}
                      currency="usd"
                      productName="Demo Product"
                      userId={user?.uid}
                      onSuccess={(paymentIntent) => {
                        console.log('Standard payment succeeded:', paymentIntent);
                      }}
                      onError={(error) => {
                        console.error('Standard payment failed:', error);
                      }}
                    />
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle>Connect Payment</CardTitle>
                    <CardDescription>
                      Payment form with Stripe Connect integration
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <ConnectEnhancedPaymentForm
                      amount={paymentAmount}
                      currency="usd"
                      productName="Connect Demo Product"
                      userId={user?.uid}
                      sellerId="demo_seller"
                      sellerAccountId={demoAccountId}
                      onSuccess={(paymentIntent) => {
                        console.log('Connect payment succeeded:', paymentIntent);
                      }}
                      onError={(error) => {
                        console.error('Connect payment failed:', error);
                      }}
                    />
                  </CardContent>
                </Card>
              </div>
            </TabsContent>
          </Tabs>
        </ConnectEmbeddedProvider>

        {/* Information Section */}
        <Card className="mt-8">
          <CardHeader>
            <CardTitle>About Connect Embedded Components</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h4 className="font-semibold mb-2">Benefits</h4>
                <ul className="text-sm text-gray-600 space-y-1">
                  <li>• No redirects - users stay on your platform</li>
                  <li>• Consistent branding and user experience</li>
                  <li>• Mobile-optimized and responsive</li>
                  <li>• Automatic compliance and security</li>
                  <li>• Real-time updates and notifications</li>
                </ul>
              </div>
              <div>
                <h4 className="font-semibold mb-2">Components Available</h4>
                <ul className="text-sm text-gray-600 space-y-1">
                  <li>• Account Onboarding</li>
                  <li>• Account Management</li>
                  <li>• Payment Management</li>
                  <li>• Payout Management</li>
                  <li>• Balance Overview</li>
                  <li>• Notification Banner</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
