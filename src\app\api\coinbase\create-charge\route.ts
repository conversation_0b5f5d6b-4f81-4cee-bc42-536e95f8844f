import { NextRequest, NextResponse } from 'next/server';
import { Client, resources } from 'coinbase-commerce-node';

// Initialize Coinbase Commerce client
if (!process.env.COINBASE_COMMERCE_API_KEY) {
  throw new Error("COINBASE_COMMERCE_API_KEY is not defined");
}

Client.init(process.env.COINBASE_COMMERCE_API_KEY);
const { Charge } = resources;

export async function POST(request: NextRequest) {
  try {
    const {
      amount,
      currency = 'USD',
      productName,
      orderId,
      userId,
      sellerId,
      userEmail,
      userName
    } = await request.json();

    if (!amount || !orderId || !userId || !sellerId) {
      return NextResponse.json({
        success: false,
        error: 'Missing required fields: amount, orderId, userId, sellerId'
      }, { status: 400 });
    }

    console.log('🪙 Creating Coinbase Commerce charge:', {
      amount,
      currency,
      productName,
      orderId
    });

    // Create charge data
    const chargeData = {
      name: productName || 'Service Order',
      description: `Payment for order ${orderId}`,
      pricing_type: 'fixed_price',
      local_price: {
        amount: (amount / 100).toFixed(2), // Convert from cents to dollars
        currency: currency.toUpperCase()
      },
      metadata: {
        orderId,
        userId,
        sellerId,
        userEmail: userEmail || '',
        userName: userName || '',
        isEscrow: 'true',
        source: 'crypto_payment'
      },
      redirect_url: `${process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000'}/payment-success?order_id=${orderId}&payment_type=crypto`,
      cancel_url: `${process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000'}/payment-cancelled?order_id=${orderId}`
    };

    // Create the charge
    const charge = await Charge.create(chargeData);

    console.log('✅ Coinbase charge created:', {
      id: charge.id,
      code: charge.code,
      hosted_url: charge.hosted_url
    });

    return NextResponse.json({
      success: true,
      chargeId: charge.id,
      chargeCode: charge.code,
      hostedUrl: charge.hosted_url,
      amount: charge.pricing.local.amount,
      currency: charge.pricing.local.currency
    });

  } catch (error) {
    console.error('❌ Error creating Coinbase charge:', error);
    
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Failed to create crypto payment'
    }, { status: 500 });
  }
}
