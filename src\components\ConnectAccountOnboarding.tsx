"use client";

import React, { useState } from 'react';
import { ConnectAccountOnboarding } from '@stripe/react-connect-js';
import { useConnectEmbedded } from './ConnectEmbeddedProvider';
import { useCurrentUser } from '@/hooks/useCurrentUser';
import { Button } from './ui/button';
import { Loader2, AlertCircle, CheckCircle } from 'lucide-react';

interface ConnectAccountOnboardingWrapperProps {
  onOnboardingComplete?: (accountId: string) => void;
  onError?: (error: string) => void;
  className?: string;
}

export const ConnectAccountOnboardingWrapper: React.FC<ConnectAccountOnboardingWrapperProps> = ({
  onOnboardingComplete,
  onError,
  className = "",
}) => {
  const { stripeConnectInstance, isLoading, error } = useConnectEmbedded();
  const { user } = useCurrentUser();
  const [onboardingStatus, setOnboardingStatus] = useState<'idle' | 'loading' | 'complete' | 'error'>('idle');
  const [statusMessage, setStatusMessage] = useState<string>('');

  const handleOnboardingExit = () => {
    // The current ConnectAccountOnboarding component doesn't pass an event payload
    // We'll just optimistically mark as complete and let the dashboard refresh status
    setOnboardingStatus('complete');
    setStatusMessage('Account onboarding completed (check status to confirm).');
    onOnboardingComplete?.('');
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
          <p className="text-gray-600">Initializing Connect components...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="text-center">
          <AlertCircle className="h-8 w-8 text-red-500 mx-auto mb-4" />
          <p className="text-red-600 mb-4">Failed to initialize Connect components</p>
          <p className="text-sm text-gray-500">{error}</p>
        </div>
      </div>
    );
  }

  if (!stripeConnectInstance) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="text-center">
          <AlertCircle className="h-8 w-8 text-yellow-500 mx-auto mb-4" />
          <p className="text-gray-600">Connect instance not available</p>
        </div>
      </div>
    );
  }

  if (onboardingStatus === 'complete') {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="text-center">
          <CheckCircle className="h-8 w-8 text-green-500 mx-auto mb-4" />
          <p className="text-green-600 mb-4">{statusMessage}</p>
          <p className="text-sm text-gray-500">You can now start accepting payments!</p>
        </div>
      </div>
    );
  }

  if (onboardingStatus === 'error') {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="text-center">
          <AlertCircle className="h-8 w-8 text-red-500 mx-auto mb-4" />
          <p className="text-red-600 mb-4">{statusMessage}</p>
          <Button 
            onClick={() => {
              setOnboardingStatus('idle');
              setStatusMessage('');
            }}
            variant="outline"
          >
            Try Again
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className={`connect-onboarding-container ${className}`}>
      <div className="mb-6">
        <h2 className="text-2xl font-bold text-gray-900 mb-2">
          Complete Your Account Setup
        </h2>
        <p className="text-gray-600">
          Please provide the required information to start accepting payments on our platform.
        </p>
      </div>
      
      <div className="border rounded-lg p-1 bg-white">
        <ConnectAccountOnboarding onExit={handleOnboardingExit} />
      </div>
      
      <div className="mt-4 text-sm text-gray-500">
        <p>
          By completing this onboarding process, you agree to Stripe's terms of service 
          and our platform's seller agreement.
        </p>
      </div>
    </div>
  );
};

export default ConnectAccountOnboardingWrapper;
