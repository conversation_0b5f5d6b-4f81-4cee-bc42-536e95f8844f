"use client";

import React, { useState, useEffect } from 'react';

interface DebugInfo {
  environment: {
    protocol: string;
    hostname: string;
    port: string;
    userAgent: string;
    isHTTPS: boolean;
  };
  browser: {
    name: string;
    isApplePaySupported: boolean;
    isGooglePaySupported: boolean;
  };
  device: {
    platform: string;
    isAppleDevice: boolean;
    hasTouchID: boolean;
  };
  stripe: {
    publishableKey: string;
    hasSecretKey: boolean;
  };
  apis: {
    createPaymentIntent: 'unknown' | 'working' | 'error';
    registerDomain: 'unknown' | 'working' | 'error';
  };
}

export default function PaymentDebugger() {
  const [debugInfo, setDebugInfo] = useState<DebugInfo | null>(null);
  const [isRunning, setIsRunning] = useState(false);
  const [testResults, setTestResults] = useState<string[]>([]);

  const addTestResult = (message: string) => {
    setTestResults(prev => [...prev, `${new Date().toLocaleTimeString()}: ${message}`]);
  };

  const runDiagnostics = async () => {
    setIsRunning(true);
    setTestResults([]);
    addTestResult('🔍 Starting comprehensive diagnostics...');

    // Environment info
    const protocol = window.location.protocol;
    const hostname = window.location.hostname;
    const port = window.location.port;
    const userAgent = navigator.userAgent;
    const isHTTPS = protocol === 'https:';

    addTestResult(`📍 Environment: ${protocol}//${hostname}${port ? ':' + port : ''}`);
    addTestResult(`🔒 HTTPS: ${isHTTPS ? '✅ Enabled' : '❌ Required for Apple Pay'}`);

    // Browser detection
    const isChrome = /Chrome/.test(userAgent);
    const isSafari = /Safari/.test(userAgent) && !/Chrome/.test(userAgent);
    const isEdge = /Edg/.test(userAgent);
    const isFirefox = /Firefox/.test(userAgent);

    let browserName = 'Unknown';
    if (isChrome) browserName = 'Chrome';
    else if (isSafari) browserName = 'Safari';
    else if (isEdge) browserName = 'Edge';
    else if (isFirefox) browserName = 'Firefox';

    addTestResult(`🌐 Browser: ${browserName} ${isSafari ? '✅ Best for Apple Pay' : '⚠️ Limited Apple Pay support'}`);

    // Device detection
    const isWindows = /Windows/.test(userAgent);
    const isMac = /Mac/.test(userAgent);
    const isiOS = /iPhone|iPad/.test(userAgent);
    const isAndroid = /Android/.test(userAgent);

    let platform = 'Unknown';
    if (isWindows) platform = 'Windows';
    else if (isMac) platform = 'macOS';
    else if (isiOS) platform = 'iOS';
    else if (isAndroid) platform = 'Android';

    const isAppleDevice = isMac || isiOS;
    addTestResult(`💻 Device: ${platform} ${isAppleDevice ? '✅ Apple Pay supported' : '❌ Apple Pay not available'}`);

    // Apple Pay availability
    const hasApplePaySession = typeof (window as any).ApplePaySession !== 'undefined';
    let canMakePayments = false;
    
    if (hasApplePaySession) {
      try {
        canMakePayments = (window as any).ApplePaySession.canMakePayments();
        addTestResult(`🍎 Apple Pay Session: ✅ Available`);
        addTestResult(`💳 Can make payments: ${canMakePayments ? '✅ Yes' : '❌ No cards in wallet'}`);
      } catch (error) {
        addTestResult(`🍎 Apple Pay Session: ❌ Error checking availability`);
      }
    } else {
      addTestResult(`🍎 Apple Pay Session: ❌ Not available (${browserName} on ${platform})`);
    }

    // Google Pay availability
    const hasGooglePay = typeof (window as any).google?.payments?.api !== 'undefined';
    addTestResult(`🔍 Google Pay API: ${hasGooglePay ? '✅ Available' : '❌ Not loaded'}`);

    // Stripe configuration
    const publishableKey = process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY;
    addTestResult(`🔑 Stripe Publishable Key: ${publishableKey ? '✅ Set' : '❌ Missing'}`);

    // Test API endpoints
    addTestResult('🧪 Testing API endpoints...');

    // Test create payment intent
    try {
      const response = await fetch('/api/create-payment-intent', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          amount: 1000,
          currency: 'usd',
          productName: 'Test Product'
        }),
      });

      if (response.ok) {
        const data = await response.json();
        if (data.success && data.clientSecret) {
          addTestResult('✅ Payment Intent API: Working');
        } else {
          addTestResult(`❌ Payment Intent API: Invalid response - ${JSON.stringify(data)}`);
        }
      } else {
        addTestResult(`❌ Payment Intent API: HTTP ${response.status}`);
      }
    } catch (error) {
      addTestResult(`❌ Payment Intent API: Network error - ${error instanceof Error ? error.message : 'Unknown'}`);
    }

    // Test domain registration
    try {
      const response = await fetch('/api/stripe/list-domains');
      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          const domainCount = data.domains?.length || 0;
          const currentDomainRegistered = data.domains?.some((d: any) => d.domain_name === hostname);
          addTestResult(`✅ Domain API: Working (${domainCount} domains registered)`);
          addTestResult(`🌐 Current domain (${hostname}): ${currentDomainRegistered ? '✅ Registered' : '❌ Not registered'}`);
        } else {
          addTestResult(`❌ Domain API: ${data.error}`);
        }
      } else {
        addTestResult(`❌ Domain API: HTTP ${response.status}`);
      }
    } catch (error) {
      addTestResult(`❌ Domain API: Network error - ${error instanceof Error ? error.message : 'Unknown'}`);
    }

    // Summary and recommendations
    addTestResult('');
    addTestResult('📋 RECOMMENDATIONS:');
    
    if (!isHTTPS) {
      addTestResult('🔒 Enable HTTPS: Use ngrok for local development');
      addTestResult('   Command: ngrok http 3000');
    }
    
    if (!isAppleDevice) {
      addTestResult('🍎 Apple Pay: Only works on Mac/iPhone/iPad');
      addTestResult('   Test with Safari on Apple device');
    }
    
    if (!isSafari && isAppleDevice) {
      addTestResult('🌐 Browser: Use Safari for best Apple Pay support');
    }
    
    if (!canMakePayments && hasApplePaySession) {
      addTestResult('💳 Apple Wallet: Add cards to test Apple Pay');
    }

    setIsRunning(false);
    addTestResult('');
    addTestResult('✅ Diagnostics complete!');
  };

  const clearResults = () => {
    setTestResults([]);
  };

  return (
    <div className="max-w-4xl mx-auto p-6 bg-white rounded-lg shadow-sm border">
      <h2 className="text-2xl font-bold mb-6">🔍 Payment System Debugger</h2>
      
      <div className="flex gap-4 mb-6">
        <button
          onClick={runDiagnostics}
          disabled={isRunning}
          className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 disabled:bg-gray-400"
        >
          {isRunning ? '🔄 Running Diagnostics...' : '🚀 Run Full Diagnostics'}
        </button>
        
        <button
          onClick={clearResults}
          className="px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700"
        >
          🗑️ Clear Results
        </button>
      </div>

      {testResults.length > 0 && (
        <div className="bg-gray-900 text-green-400 p-4 rounded font-mono text-sm max-h-96 overflow-y-auto">
          {testResults.map((result, index) => (
            <div key={index} className="mb-1">
              {result}
            </div>
          ))}
        </div>
      )}

      {!isRunning && testResults.length === 0 && (
        <div className="text-center text-gray-500 py-8">
          Click "Run Full Diagnostics" to analyze your payment setup
        </div>
      )}
    </div>
  );
}
