import { useEffect, useState } from "react";
import { GlobalCard } from "@/globalComponents/globalCard";
import useAuth from "@/hook";
import { getServicesByCategory } from "@/services/serviceService";
import { getUserByServicesId } from "@/services/usersServices";
import { User } from "@/services/UserInterface";
import { themes } from "../../../../theme";
import { AlertCircle } from "react-feather";
import CardSkeleton from "@/components/CardSkeleton";
import { useRouter } from "next/navigation";
import { useFilter } from "@/context/FilterContext";

const ServicesCard = (props: any) => {
  const user = useAuth();
  const router = useRouter();
  const { filters, getServiceFilters } = useFilter();
  const [loading, setLoading] = useState<boolean>(true);
  const [services, setServices]: any = useState([]);
  const [error, setError] = useState<string | null>(null);
  const [retryCount, setRetryCount] = useState(0);
  const [userLoading, setUserLoading] = useState<string | null>(null);

  useEffect(() => {
    const fetchAllServices = async () => {
      setLoading(true);
      setError(null);
      try {
        if (!props.themeProperties?.title) {
          throw new Error("Category title is missing");
        }

        const currentCategory =
          props.themeProperties.title === "My Feed" ? "My Feed" : props.themeProperties.title;

        // Check if filters are applied and if current category is in selected categories
        if (filters.categories && filters.categories.length > 0) {
          if (!filters.categories.includes(currentCategory)) {
            // Current category is not in selected filters, show no data
            setServices([]);
            setLoading(false);
            return;
          }
        }

        // Get complete filter object for service call
        const serviceFilters = getServiceFilters();

        // Fetch services for the current category with all filters
        const responsedan = await getServicesByCategory(
          currentCategory,
          user?.userId,
          serviceFilters
        );
        if (!responsedan || !responsedan.success) {
          throw new Error(responsedan?.error || "Failed to fetch services");
        }
        setServices(responsedan.services || []);
      } catch (err: any) {
        console.error("Error fetching services:", err);
        setError(err.message || "An error occurred while fetching services");
        setServices([]);
      } finally {
        setLoading(false);
      }
    };
    fetchAllServices();
  }, [props, user?.userId, retryCount, filters]);

  const handleRetry = () => {
    setRetryCount((prev) => prev + 1);
  };

  const handleServiceClick = async (serviceId: string) => {
    setUserLoading(serviceId);
    const result = await getUserByServicesId(serviceId);
    setUserLoading(null);
    if (result.success && result.users && result.users.length > 0) {
      const user = result.users[0] as User;
      if (user.profile_name) {
        router.push(`/profile/amuzn/${user.profile_name.replace(/\s+/g, "-")}?view=Services`);
      } else {
        alert("User found but profile name is missing.");
      }
    } else {
      alert("User not found for this service.");
    }
  };

  if (loading) {
    return (
      <div className="">
        <CardSkeleton count={16} columns={4} showGrid={true} />
      </div>
    );
  }

  if (error) {
    return (
      <div className="w-full mt-2 p-4 bg-red-50 rounded-md border border-red-200">
        <div className="flex items-center gap-2 text-red-600 mb-2">
          <AlertCircle size={20} />
          <h3 className="font-medium">Error loading services</h3>
        </div>
        <p className="text-red-600 mb-4">{error}</p>
        <button
          onClick={handleRetry}
          className="px-4 py-2 bg-white border border-red-300 rounded-md hover:bg-red-50 transition-colors text-red-600"
        >
          Try Again
        </button>
      </div>
    );
  }

  if (!services || services.length === 0) {
    return (
      <div className="w-full mt-2 p-4 bg-gray-50 rounded-md border border-gray-200 text-center">
        <p className="text-gray-500">No services found in this category.</p>
      </div>
    );
  }

  return (
    <div className="w-full mt-2 grid grid-cols-4 max-md:grid-cols-1 max-lg:grid-cols-2 gap-3">
      {services.map((item: any, index: number) => (
        <div key={index} className="mb-2">
          {Object.entries(themes).map(([_, innerThemeProperties]) => (
            <div key={innerThemeProperties.title}>
              {(item.category === "Storytelling" ? "Literature" : item.category) ===
                innerThemeProperties.title && (
                <button
                  onClick={() => handleServiceClick(item.id)}
                  className="w-full text-left"
                  disabled={userLoading === item.id}
                  style={{ opacity: userLoading === item.id ? 0.6 : 1 }}
                >
                  <GlobalCard
                    border={innerThemeProperties.backgroundColor}
                    title={item.title || "Untitled Service"}
                    duration={item.duration}
                    description={item.description || "No description available"}
                    price={item.price}
                    currency={item.currency || "GBP"}
                  />
                </button>
              )}
            </div>
          ))}
        </div>
      ))}
    </div>
  );
};

export default ServicesCard;
