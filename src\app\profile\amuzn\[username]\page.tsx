// "use client";

import { useSearchParams } from "next/navigation";
import Profile from "@/screens/profile";

interface AmuznProfilePageProps {
  params: Promise<{
    username: string;
  }>;
}

const AmuznProfilePage = async ({ params }: AmuznProfilePageProps) => {
  const { username } = await params;

  return (
    <Profile
      userId='test'
      profileType="amuzn"
      profile_name={username}
    />
  );
};

export default AmuznProfilePage;
