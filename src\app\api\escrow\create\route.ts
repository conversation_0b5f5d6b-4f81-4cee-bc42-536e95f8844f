import { NextRequest, NextResponse } from 'next/server';
import { Stripe } from 'stripe';
import { updateOrder } from '@/services/ordersServices';
import { updateOrderStripeDetailsAdmin } from '@/services/ordersAdminServices';
import { getCompleteSellerInfo } from '@/services/sellerService';

if (!process.env.STRIPE_SECRET_KEY) {
  throw new Error("STRIPE_SECRET_KEY is not defined");
}

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY);

export async function POST(request: NextRequest) {
  try {
    const {
      userId,
      userEmail,
      sellerId,
      sellerEmail,
      sellerStripeAccountId,
      orderId,
      amount,
      currency = 'usd',
      productName,
      productDescription,
      isInvoice,
      formatDate
    } = await request.json();


    // Validate required fields - sellerId is required, but other seller fields can be auto-resolved
    if (!userId || !userEmail || !sellerId || !orderId || !amount) {
      return NextResponse.json({
        error: 'Missing required fields: userId, userEmail, sellerId, orderId, amount'
      }, { status: 400 });
    }

    // Auto-resolve seller information if not provided
    let finalSellerEmail = sellerEmail;
    let finalSellerStripeAccountId = sellerStripeAccountId;

    if (!sellerEmail || !sellerStripeAccountId) {

      
      const sellerResult = await getCompleteSellerInfo(sellerId);

      if (!sellerResult.success || !sellerResult.seller) {
        return NextResponse.json({
          error: `Seller not found or not properly set up: ${sellerResult.error}`
        }, { status: 400 });
      }

      finalSellerEmail = sellerEmail || sellerResult.seller.userEmail || sellerResult.seller.email;
      finalSellerStripeAccountId = sellerStripeAccountId || sellerResult.seller.stripeAccountId;

      if (!finalSellerEmail) {
        return NextResponse.json({
          error: 'Seller email not found. Please provide sellerEmail or ensure seller has email in profile.'
        }, { status: 400 });
      }

      if (!finalSellerStripeAccountId) {
        return NextResponse.json({
          error: 'Seller Stripe account not found. Seller must complete onboarding first.'
        }, { status: 400 });
      }

      console.log('Resolved seller info:', {
        sellerId,
        email: finalSellerEmail,
        stripeAccountId: finalSellerStripeAccountId
      });
    }

    // Validate seller's Stripe account
    try {
      const account = await stripe.accounts.retrieve(finalSellerStripeAccountId);
      if (!account.charges_enabled || !account.payouts_enabled) {
        return NextResponse.json({
          error: 'Seller account is not fully set up for payments'
        }, { status: 400 });
      }
      console.log('Seller Stripe account validated:', {
        accountId: finalSellerStripeAccountId,
        chargesEnabled: account.charges_enabled,
        payoutsEnabled: account.payouts_enabled
      });
    } catch (error) {
      console.error('Error validating seller Stripe account:', error);
      return NextResponse.json({
        error: 'Invalid seller Stripe account'
      }, { status: 400 });
    }

    // Get or create customer
    let customer;
    try {
      console.log('Looking for existing customer with email:', userEmail);
      const customers = await stripe.customers.list({
        email: userEmail,
        limit: 1
      });

      if (customers.data.length > 0) {
        customer = customers.data[0];
        console.log('Found existing customer:', customer.id);
      } else {
        console.log('Creating new customer for user:', userId);
        customer = await stripe.customers.create({
          email: userEmail,
          metadata: {
            firebaseUid: userId
          }
        });
        console.log('Created new customer:', customer.id);
      }
    } catch (error) {
      console.error('Error creating customer:', error);
      // If customer with this ID already exists, retrieve it
      try {
        console.log('Attempting to find customer by Firebase UID:', userId);
        // Try to find customer by Firebase UID in metadata
        const existingCustomers = await stripe.customers.list({
          limit: 100
        });

        const foundCustomer = existingCustomers.data.find(c => c.metadata?.firebaseUid === userId);
        if (foundCustomer) {
          customer = foundCustomer;
          console.log('Found customer by Firebase UID:', customer.id);
        } else {
          throw new Error('No customer found with this Firebase UID');
        }
      } catch (retrieveError) {
        console.error('Error retrieving customer:', retrieveError);
        const createErrorMessage = error instanceof Error ? error.message : 'Unknown create error';
        const retrieveErrorMessage = retrieveError instanceof Error ? retrieveError.message : 'Unknown retrieve error';

        return NextResponse.json({
          error: 'Failed to create or retrieve customer',
          createError: createErrorMessage,
          retrieveError: retrieveErrorMessage,
          userId,
          userEmail
        }, { status: 500 });
      }
    }

    // Skipping separate transactions collection write to avoid permission issues.
    // We'll mark the order as escrow-enabled via PaymentIntent metadata and store PI id on the order.

    // Step 1: Create Payment Intent with manual capture for escrow
    console.log('🔄 Step 1: Creating Payment Intent with manual capture for escrow...');
    const paymentIntent = await stripe.paymentIntents.create({
      amount: amount,
      currency: currency.toLowerCase(),
      customer: customer.id,
      capture_method: 'manual', // This authorizes payment but doesn't capture it
      setup_future_usage: 'off_session',
      automatic_payment_methods: {
        enabled: true,
        allow_redirects: 'never',
      },
      metadata: {
        userId,
        sellerId,
        orderId,
        isEscrow: 'true',
        escrowStage: 'authorized',
      userEmail,
      sellerEmail,
      sellerStripeAccountId,
      amount,
      currency,
      productName,
      productDescription,
      isInvoice,
      formatDate
      },
      description: `Escrow payment for ${productName} - Order ${orderId}`,
    });

    console.log('✅ Payment Intent created with manual capture:', {
      paymentIntentId: paymentIntent.id,
      clientSecret: paymentIntent.client_secret,
      status: paymentIntent.status
    });

    // Store PaymentIntent id on the order via OrdersServices
    try {
      await updateOrder(orderId, { payment_intent_id: paymentIntent.id });
    } catch (e) {
      console.warn('Failed to update order with payment_intent_id:', e);
      // Do not fail the request; client can retry storing PI id later
    }

    // Also set transactionId on the order (chargeId not yet available)
    try {
      await updateOrderStripeDetailsAdmin({ id: orderId, chargeId: '', transactionId: paymentIntent.id });
    } catch (e) {
      console.warn('Failed to update order Stripe details (transactionId):', e);
    }

    return NextResponse.json({
      success: true,
      transactionId:paymentIntent.id, 
      // transactionResult.transactionId,
      paymentIntentId: paymentIntent.id,
      clientSecret: paymentIntent.client_secret,
      customerId: customer.id,
      orderId,
      amount,
      currency,
      productName,
      productDescription,
      sellerId,
      sellerEmail: finalSellerEmail,
      sellerStripeAccountId: finalSellerStripeAccountId,
      message: 'Escrow transaction created successfully. Use the client secret for embedded payment.',
      escrowStages: {
        accept: '10%',
        delivered: '10%',
        completed: '80%'
      },
      paymentFlow: {
        step1: 'User completes payment via embedded form (authorizes payment)',
        step2: 'Payment captured when order accepted (10% to seller)',
        step3: 'Remaining stages: 10% delivered, 80% completed'
      },
      nextSteps: {
        userAction: 'Use clientSecret with Stripe Elements to complete payment',
        afterPayment: 'Use capture-and-release endpoint when order is accepted'
      }
    });
  } catch (error) {
    console.error('Escrow checkout error:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    return NextResponse.json({ error: errorMessage }, { status: 500 });
  }
}
