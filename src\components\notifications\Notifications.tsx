import React, { useEffect, useState } from 'react';
import { Lo<PERSON>, Settings } from 'react-feather';
import { NotificationManager, Notifications as NotificationType, NotificationEvents } from '@/services/notificationService';
import useAuth from '@/hook';
import { Timestamp } from 'firebase/firestore';
import Link from 'next/link';
import { generateFileUrl } from '@/lib/utils';

interface NotificationsProps {
  onOpenChange?: (open: boolean) => void;
  onDrawerChange?: (open: boolean) => void;
}

const NotificationsComponent = ({ onOpenChange, onDrawerChange }: NotificationsProps) => {
  const [notifications, setNotifications] = useState<NotificationType[]>([]);
  const [loading, setLoading] = useState(true);
  const user = useAuth();

  // const generateFileUrl = (postFile: string | undefined): string | undefined => {
  //   const baseUrl = process.env.BASE_STORAGE_URL;
  //   if (!baseUrl) return undefined;

  //   if (!postFile) {
  //     return undefined;
  //   }

  //   // this is for handleing both dev/prod db urls
  //   if (postFile.startsWith("https://firebasestorage.googleapis.com/")) {
  //     return postFile;
  //   }

  //   return `${baseUrl}${encodeURIComponent(postFile)}?alt=media`;
  // };

  const getInitials = (name: string | undefined): string => {
    if (!name) return "";

    const nameParts = name.trim().split(/\s+/);
    if (nameParts.length === 1) {
      return nameParts[0].charAt(0).toUpperCase();
    } else {
      return (nameParts[0].charAt(0) + nameParts[nameParts.length - 1].charAt(0)).toUpperCase();
    }
  };
  useEffect(() => {
    const fetchNotifications = async () => {
      console.log('Starting to fetch notifications...');
      setLoading(true); // Explicitly set loading to true at the start
      
      try {
        if (!user?.userId) {
          console.log('No user ID available:', { user });
          setLoading(false);
          return;
        }
        
        // console.log('Fetching notifications for user:', user.userId);
        const resp = await NotificationManager.getInstance().GetNotificationsByUserId({
          user_id: user.userId
        });
        // console.log('Notifications response:', resp);
        setNotifications(resp);
      } catch (error) {
        console.error('Error fetching notifications:', error);
      } finally {
        console.log('Setting loading to false');
        setLoading(false);
      }
    };

    fetchNotifications();
  }, [user?.userId]);

//   console.log('Current loading state:', loading);

  const formatTimeAgo = (timestamp: Timestamp | undefined) => {
    if (!timestamp) return '';
    
    const date = timestamp instanceof Timestamp 
      ? timestamp.toDate() 
      : new Date(timestamp);
    const now = new Date();
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);

    if (diffInSeconds < 60) return 'just now';
    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m`;
    if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h`;
    if (diffInSeconds < 604800) return `${Math.floor(diffInSeconds / 86400)}d`;
    if (diffInSeconds < 2592000) return `${Math.floor(diffInSeconds / 604800)}w`;
    return `${Math.floor(diffInSeconds / 2592000)}mo`;
  };

  const getNotificationMessage = (event: NotificationEvents) => {
    switch (event) {
      case NotificationEvents.COMMENT:
        return 'commented on your post';
      case NotificationEvents.REACTION:
        return 'reacted to your post';
      case NotificationEvents.BOOKMARK:
        return 'bookmarked your post';
      case NotificationEvents.FOLLOW:
        return 'started following you';
      case NotificationEvents.POST_UPLOAD:
        return 'uploaded a new post';
      case NotificationEvents.SERVICE_UPLOAD:
        return 'uploaded a new service';
      case NotificationEvents.EVENT_UPLOAD:
        return 'created a new event';
      case NotificationEvents.ORDER_STATUS_CHANGE:
        return 'changed to order status of order';
      default:
        return 'interacted with your content';
    }
  };

  if (loading) {
    console.log('Rendering loading state');
    return (
      <div className="flex flex-col items-center justify-center h-[calc(100vh-100px)] space-y-4">
        <div className="relative">
          <Loader size={48} className="text-primary animate-spin" />
          <div className="absolute inset-0 flex items-center justify-center">
            <div className="w-4 h-4 bg-white rounded-full"></div>
          </div>
        </div>
        <p className="text-sm text-gray-500">Loading notifications...</p>
      </div>
    );
  }

  if (notifications.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center h-40 space-y-4">
        <div className="w-16 h-16 rounded-full bg-gray-100 flex items-center justify-center">
          <Settings className="w-8 h-8 text-gray-400" />
        </div>
        <div className="text-center">
          <p className="text-base font-medium text-gray-900">No notifications yet</p>
          <p className="text-sm text-gray-500 mt-1">We'll notify you when something happens</p>
        </div>
      </div>
    );
  }

  console.log(notifications);
  
  return (
    <div className="space-y-6">
      {/* This Month */}
      {notifications.filter(notification => {
        if (!notification.timestamp) return false;
        const date = notification.timestamp instanceof Timestamp 
          ? notification.timestamp.toDate() 
          : new Date(notification.timestamp);
        const now = new Date();
        return date.getMonth() === now.getMonth() && date.getFullYear() === now.getFullYear();
      }).length > 0 && (
        <div>
          <h3 className="text-sm font-semibold text-gray-500 mb-4">This Month</h3>
          <div className="space-y-4">
            {notifications.filter(notification => {
              if (!notification.timestamp) return false;
              const date = notification.timestamp instanceof Timestamp 
                ? notification.timestamp.toDate() 
                : new Date(notification.timestamp);
              const now = new Date();
              return date.getMonth() === now.getMonth() && date.getFullYear() === now.getFullYear();
            }).map((notification) => (
              <div key={notification.id}>
                <div className="flex items-center gap-3">
                  <div className="w-10 h-10 rounded-full overflow-hidden bg-gray-200 flex items-center justify-center">
                    {/* <Settings className="w-6 h-6 text-gray-400" /> */}
                    {generateFileUrl(notification?.src_details?.avatar) ? (
                    <img
                            src={generateFileUrl(notification?.src_details?.avatar)}
                            alt="Profile"
                            className="w-10 h-10rounded-full"
                          />
                        
                        ) : (
                          <div className="w-10 h-10 rounded-full bg-[#BDBDBD] flex items-center justify-center text-white font-bold text-base">
                            {getInitials(notification?.src_details?.profile_name|| "Profile Name")}
                          </div>
                        )}
                  </div>
                  
                  <div className="flex-1">
                    <Link href={`/profile/amuzn/${notification?.src_details?.profile_name?.replace(/\s+/g, '-')}`} 
                          className="text-sm text-gray-800 cursor-pointer"
                          onClick={() => {
                            onOpenChange?.(false);
                            onDrawerChange?.(false);
                          }}>
                      <span className="font-semibold">{notification?.src_details?.profile_name ? notification?.src_details?.profile_name : 'Profile Name'}</span>
                      {' '}{getNotificationMessage(notification.event)} 
                      {' '} {notification?.order_status ? (
                        notification?.order_id +" to " + notification?.order_status):""}
                      
                    </Link>
                    <p className="text-xs text-gray-500">{formatTimeAgo(notification.timestamp)}</p>
                  </div>
                   
                  {notification.post_url && (
                    <Link href={`/browse/${
                      notification.src_details.categories[0] === "Storytelling" ? "Literature" : notification.src_details.categories[0]
                    }/${notification.post_id}%20${notification.src_id}`}  className="w-10 h-10 rounded-lg overflow-hidden">
                      <img 
                        src={generateFileUrl(notification.post_url)} 
                        alt="Post thumbnail"
                        className="w-full h-full object-cover"
                      />
                    </Link>
                  )}
                 
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Earlier */}
      {notifications.filter(notification => {
        if (!notification.timestamp) return false;
        const date = notification.timestamp instanceof Timestamp 
          ? notification.timestamp.toDate() 
          : new Date(notification.timestamp);
        const now = new Date();
        return date.getMonth() !== now.getMonth() || date.getFullYear() !== now.getFullYear();
      }).length > 0 && (
        <div>
          <h3 className="text-sm font-semibold text-gray-500 mb-4">Earlier</h3>
          <div className="space-y-4">
            {notifications.filter(notification => {
              if (!notification.timestamp) return false;
              const date = notification.timestamp instanceof Timestamp 
                ? notification.timestamp.toDate() 
                : new Date(notification.timestamp);
              const now = new Date();
              return date.getMonth() !== now.getMonth() || date.getFullYear() !== now.getFullYear();
            }).map((notification) => (
              <div key={notification.id}>
                <div className="flex items-center gap-3">
                  <div className="w-10 h-10 rounded-full overflow-hidden bg-gray-200 flex items-center justify-center">
                    {generateFileUrl(notification?.src_details?.avatar) ? (
                      <img
                        src={generateFileUrl(notification?.src_details?.avatar)}
                        alt="Profile"
                        className="w-10 h-10rounded-full"
                      />
                    ) : (
                      <div className="w-10 h-10 rounded-full bg-[#BDBDBD] flex items-center justify-center text-white font-bold text-base">
                        {getInitials(notification?.src_details?.profile_name|| "Profile Name")}
                      </div>
                    )}
                  </div>
                  <div className="flex-1">
                    <Link href={`/profile/amuzn/${notification?.src_details?.profile_name?.replace(/\s+/g, '-')}`} 
                          className="text-sm text-gray-800 cursor-pointer"
                          onClick={() => {
                            onOpenChange?.(false);
                            onDrawerChange?.(false);
                          }}>
                      <span className="font-semibold">{notification?.src_details?.profile_name ? notification?.src_details?.profile_name : 'Profile Name'}</span>
                      {' '}{getNotificationMessage(notification.event)}
                    </Link>
                    <p className="text-xs text-gray-500">{formatTimeAgo(notification.timestamp)}</p>
                  </div>
                  {notification.post_url && (
                    <Link href={`/browse/${
                      notification.src_details.categories[0] === "Storytelling" ? "Literature" : notification.src_details.categories[0]
                    }/${notification.post_id}%20${notification.src_id}`}  className="w-10 h-10 rounded-lg overflow-hidden">
                      <img 
                        src={generateFileUrl(notification.post_url)} 
                        alt="Post thumbnail"
                        className="w-full h-full object-cover"
                      />
                    </Link>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default NotificationsComponent; 