import { NextRequest, NextResponse } from 'next/server';
import { Stripe } from 'stripe';
import { getUserIdFromRequest } from '@/lib/auth/serverAuth';
import { doc, setDoc } from 'firebase/firestore';
import { initFirebase } from '../../../../../firebaseConfig';

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY as string);

export async function POST(req: NextRequest) {
  try {
    // Parse request body first (to avoid consuming it later in auth helper)
    let body: any = {};
    try {
      body = await req.json();
    } catch (e) {
      // Request may not have JSON body; continue with empty body
      console.warn('Body parse skipped or failed in /api/connect/login:', e);
    }
    const { accountId, email, businessName } = body || {};

    // Get user ID from request (headers or query). Fallback to body.userId
    let userId = await getUserIdFromRequest(req);
    if (!userId) {
      userId = body?.userId;
    }

    if (!userId) {
      return NextResponse.json(
        {
          error: 'Authentication required. Please log in to connect your Stripe account.',
          code: 'AUTH_REQUIRED'
        },
        { status: 401 }
      );
    }

    // Validate that either accountId or email is provided
    if (!accountId && !email) {
      return NextResponse.json(
        { error: 'Either Stripe Account ID or email is required to connect existing account.' },
        { status: 400 }
      );
    }

    console.log('Connecting existing Stripe account for user:', userId);

    let account;

    if (accountId) {
      // Method 1: Connect using existing account ID
      try {
        account = await stripe.accounts.retrieve(accountId);
        
        // Verify the account exists and is valid
        if (!account || account.object !== 'account') {
          return NextResponse.json(
            { error: 'Invalid account ID or account not found.' },
            { status: 404 }
          );
        }

        console.log('Found existing account:', account.id);
      } catch (error) {
        console.error('Error retrieving account by ID:', error);
        return NextResponse.json(
          { error: 'Account not found or invalid account ID.' },
          { status: 404 }
        );
      }
    } else if (email) {
      // Method 2: Search for account by email (limited capability)
      // Note: Stripe doesn't provide direct email search, so we'll create a new account
      // and let the user complete the connection process
      try {
        // Check if account with this email already exists by creating a new one
        // If it exists, Stripe will return an error with account details
        account = await stripe.accounts.create({
          type: 'express',
          email: email,
          business_profile: businessName ? { name: businessName } : undefined
        });

        console.log('Created new account for existing user:', account.id);
      } catch (error: any) {
        // If account already exists, Stripe might return specific error
        if (error.code === 'account_already_exists') {
          return NextResponse.json(
            { error: 'An account with this email already exists. Please provide the account ID instead.' },
            { status: 409 }
          );
        }
        
        console.error('Error creating account:', error);
        return NextResponse.json(
          { error: 'Failed to connect account. Please check your email or provide account ID.' },
          { status: 500 }
        );
      }
    }

    // Ensure account was successfully retrieved or created
    if (!account) {
      return NextResponse.json(
        { error: 'Failed to connect or retrieve Stripe account.' },
        { status: 500 }
      );
    }

    // Save account to Firebase in both users (stripe_id) and stripeAccounts collections
    try {
      const { db } = await initFirebase();

      // 1) Update user document with stripe_id
      const userRef = doc(db, 'users', userId);
      await setDoc(userRef, { stripe_id: account.id }, { merge: true });

      // 2) Upsert stripeAccounts document (for metadata)
      const stripeAccRef = doc(db, 'stripeAccounts', account.id);

      // Build payload without undefined values (Firestore disallows undefined)
      const stripeAccountPayload: any = {
        userId,
        stripeAccountId: account.id,
        connectedAt: new Date().toISOString(),
        onboardingComplete: account?.details_submitted || false,
        chargesEnabled: account?.charges_enabled || false,
        payoutsEnabled: account?.payouts_enabled || false,
      };
      const resolvedEmail = account?.email ?? email ?? null;
      const resolvedBusiness = account?.business_profile?.name ?? businessName ?? null;
      if (resolvedEmail !== undefined) stripeAccountPayload.email = resolvedEmail;
      if (resolvedBusiness !== undefined) stripeAccountPayload.businessName = resolvedBusiness;

      await setDoc(
        stripeAccRef,
        stripeAccountPayload,
        { merge: true }
      );

      console.log('Saved connected Stripe account to Firebase:', {
        userId,
        accountId: account.id,
        email: account.email,
      });
    } catch (firebaseError) {
      console.error('Error saving to Firebase:', firebaseError);
      return NextResponse.json(
        { error: 'Account connected but failed to save to database. Please try again.' },
        { status: 500 }
      );
    }

    // Create account link for any additional setup if needed
    const origin = req.headers.get('origin') || 'http://localhost:3000';
    let accountLinkUrl = null;

    if (!account.details_submitted) {
      try {
        const accountLink = await stripe.accountLinks.create({
          account: account.id,
          refresh_url: `${origin}/payment?userId=${userId}`,
          return_url: `${origin}/payment?account=${account.id}&userId=${userId}&connected=true`,
          type: 'account_onboarding',
        });
        accountLinkUrl = accountLink.url;
      } catch (linkError) {
        console.error('Error creating account link:', linkError);
        // Continue without link - account is still connected
      }
    }

    return NextResponse.json({
      success: true,
      message: 'Account connected successfully',
      accountId: account.id,
      userId,
      account: {
        id: account.id,
        email: account.email,
        businessName: account.business_profile?.name,
        detailsSubmitted: account.details_submitted,
        chargesEnabled: account.charges_enabled,
        payoutsEnabled: account.payouts_enabled,
        country: account.country,
        currency: account.default_currency
      },
      onboardingUrl: accountLinkUrl
    });
  } catch (error) {
    console.error('Error connecting Stripe account:', error);
    return NextResponse.json(
      { error: 'Failed to connect account' },
      { status: 500 }
    );
  }
}
