import { NextRequest, NextResponse } from "next/server";

// Note: <PERSON>e's Onramp API is not yet covered by stripe-node in some versions.
// We'll call the HTTP endpoint directly.
// POST https://api.stripe.com/v1/crypto/onramp_sessions

function usdFromCents(cents: number) {
  return Math.max(0, Math.round(cents)) / 100;
}

export async function POST(request: NextRequest) {
  try {
    const STRIPE_SECRET_KEY = process.env.STRIPE_SECRET_KEY;
    if (!STRIPE_SECRET_KEY) {
      return NextResponse.json({ error: "Missing STRIPE_SECRET_KEY" }, { status: 500 });
    }

    const body = await request.json();
    const {
      amount, // in cents
      currency='usd',
      orderId,
      userId,
      sellerId,
      productName,
      walletAddress, // optional, if you want to prefill and/or lock
      lockWalletAddress = false,
      destination_currency, // e.g., "eth", "usdc"
      destination_network, // e.g., "ethereum", "polygon"
      customer_information, // optional prefill (email, name, address, dob)
    } = body || {};

    if (!orderId || !userId || !sellerId || !amount) {
      return NextResponse.json(
        { error: "Missing required fields: amount, orderId, userId, sellerId" },
        { status: 400 }
      );
    }

    // Onramp supports USD as source_currency. Convert cents -> dollars string
    const source_amount = usdFromCents(Number(amount));

    // Best-effort customer IP for supportability checks
    const xff = request.headers.get("x-forwarded-for") || "";
    const customer_ip_address = xff.split(",")[0]?.trim();

    const form = new URLSearchParams();
    form.set("client_reference_id", String(orderId));
    form.set("source_currency", currency);
    form.set("source_amount", String(source_amount));

    if (customer_ip_address) {
      form.set("customer_ip_address", customer_ip_address);
    }

    if (productName) {
      // This is not a documented field; keeping in case of future metadata support.
      // You can add metadata via `metadata[...]=...` style when supported by the API.
    }

    if (destination_currency) {
      form.set("destination_currency", String(destination_currency));
    }
    if (destination_network) {
      form.set("destination_network", String(destination_network));
    }

    if (walletAddress) {
      form.set("wallet_addresses[default]", String(walletAddress));
      if (lockWalletAddress) {
        form.set("lock_wallet_address", "true");
      }
    }

    // Optional: customer_information prefill
    if (customer_information && typeof customer_information === "object") {
      const ci = customer_information as any;
      if (ci.email) form.set("customer_information[email]", String(ci.email));
      if (ci.first_name) form.set("customer_information[first_name]", String(ci.first_name));
      if (ci.last_name) form.set("customer_information[last_name]", String(ci.last_name));
      if (ci.dob) {
        if (ci.dob.year) form.set("customer_information[dob][year]", String(ci.dob.year));
        if (ci.dob.month) form.set("customer_information[dob][month]", String(ci.dob.month));
        if (ci.dob.day) form.set("customer_information[dob][day]", String(ci.dob.day));
      }
      if (ci.address) {
        if (ci.address.country) form.set("customer_information[address][country]", String(ci.address.country));
        if (ci.address.line1) form.set("customer_information[address][line1]", String(ci.address.line1));
        if (ci.address.line2) form.set("customer_information[address][line2]", String(ci.address.line2));
        if (ci.address.city) form.set("customer_information[address][city]", String(ci.address.city));
        if (ci.address.state) form.set("customer_information[address][state]", String(ci.address.state));
        if (ci.address.postal_code) form.set("customer_information[address][postal_code]", String(ci.address.postal_code));
      }
    }

    const resp = await fetch("https://api.stripe.com/v1/crypto/onramp_sessions", {
      method: "POST",
      headers: {
        Authorization: `Bearer ${STRIPE_SECRET_KEY}`,
        "Content-Type": "application/x-www-form-urlencoded",
      },
      body: form,
    });

    const json = await resp.json();
    if (!resp.ok) {
      console.error("Onramp session create failed:", json);
      return NextResponse.json({ success: false, error: json }, { status: resp.status });
    }

    return NextResponse.json({ success: true, session: json, clientSecret: json.client_secret });
  } catch (err) {
    console.error("Error creating onramp session:", err);
    return NextResponse.json({ success: false, error: (err as Error).message }, { status: 500 });
  }
}

