# Coinbase Commerce Setup Guide

## 1. Create Coinbase Commerce Account

1. Go to [https://commerce.coinbase.com/](https://commerce.coinbase.com/)
2. Sign up or log in with your Coinbase account
3. Complete business verification if required

## 2. Get API Credentials

### API Key:
1. Go to **Settings** → **API Keys**
2. Click **Create an API Key**
3. Copy the API Key and add to `.env.local`:
   ```
   COINBASE_COMMERCE_API_KEY=your_api_key_here
   ```

### Webhook Secret:
1. Go to **Settings** → **Webhook subscriptions**
2. Click **Add an endpoint**
3. Enter your webhook URL: `https://yourdomain.com/api/coinbase/webhook`
4. Select events: `charge:confirmed`, `charge:failed`, `charge:delayed`
5. Copy the **Signing secret** and add to `.env.local`:
   ```
   COINBASE_COMMERCE_WEBHOOK_SECRET=your_webhook_secret_here
   ```

## 3. Environment Variables

Add these to your `.env.local` file:

```env
# Coinbase Commerce
COINBASE_COMMERCE_API_KEY=your_coinbase_api_key_here
COINBASE_COMMERCE_WEBHOOK_SECRET=your_webhook_secret_here
NEXT_PUBLIC_BASE_URL=http://localhost:3000  # Change to your domain in production
```

## 4. Supported Cryptocurrencies

Coinbase Commerce supports:
- Bitcoin (BTC)
- Ethereum (ETH)
- Litecoin (LTC)
- Bitcoin Cash (BCH)
- USD Coin (USDC)
- DAI

## 5. Testing

### Test Mode:
- Use Coinbase Commerce in test mode for development
- Test payments won't process real cryptocurrency
- Use test webhook endpoints

### Production:
- Switch to live mode in Coinbase Commerce dashboard
- Update webhook URL to production domain
- Ensure SSL certificate is valid

## 6. Webhook Events

Your webhook endpoint (`/api/coinbase/webhook`) handles:

- `charge:confirmed` - Payment completed successfully
- `charge:failed` - Payment failed
- `charge:delayed` - Payment pending blockchain confirmation
- `charge:pending` - Payment initiated but not confirmed
- `charge:resolved` - Previously delayed payment confirmed

## 7. Integration Flow

1. User selects crypto payment method
2. System creates Coinbase Commerce charge
3. User redirected to Coinbase Commerce hosted page
4. User pays with cryptocurrency
5. Coinbase sends webhook to your endpoint
6. Your system updates order status
7. User redirected back to success page

## 8. Security Notes

- Keep API keys secure and never expose in frontend code
- Verify webhook signatures to prevent fraud
- Use HTTPS for all webhook endpoints
- Validate all webhook data before processing

## 9. Fees

Coinbase Commerce charges:
- 1% fee for cryptocurrency payments
- Automatic conversion to fiat currency
- Settlement in your preferred currency (USD, EUR, GBP, etc.)

## 10. Support

- [Coinbase Commerce Documentation](https://commerce.coinbase.com/docs/)
- [API Reference](https://commerce.coinbase.com/docs/api/)
- [Webhook Guide](https://commerce.coinbase.com/docs/api/#webhooks)
