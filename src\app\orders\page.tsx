"use client";

import { useState, useEffect, Suspense } from 'react';
import { useSearchParams } from 'next/navigation';
import { useCurrentUser } from '@/hooks/useCurrentUser';
import { GetOrderDetailsByUserId } from '@/services/ordersServices';
import { OrderStatusType } from '@/lib/constant';
import { Order } from '@/services/ordersServices';

function OrdersPageContent() {
  const { user } = useCurrentUser();
  const searchParams = useSearchParams();
  const highlightOrderId = searchParams.get('highlight');
  
  const [orders, setOrders] = useState<Order[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<'my-orders' | 'received-orders'>('my-orders');

  useEffect(() => {
    if (user?.uid) {
      fetchOrders();
    }
  }, [user?.uid, activeTab]);

  const fetchOrders = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const result = await GetOrderDetailsByUserId({
        userId: user!.uid,
      });

      if (result.success) {
        // Filter orders based on active tab
        const filteredOrders = activeTab === 'my-orders' 
          ? result.myOrders || []
          : result.receivedOrders || [];
        
        setOrders(filteredOrders);
      } else {
        setError(result.error || 'Failed to fetch orders');
      }
    } catch (err) {
      console.error('Error fetching orders:', err);
      setError('Failed to fetch orders');
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case OrderStatusType.NEW:
        return 'bg-blue-100 text-blue-800';
      case OrderStatusType.ACCEPTED:
        return 'bg-green-100 text-green-800';
      case OrderStatusType.DELIVERED:
        return 'bg-purple-100 text-purple-800';
      case OrderStatusType.COMPLETED:
        return 'bg-emerald-100 text-emerald-800';
      case OrderStatusType.DECLINED:
        return 'bg-red-100 text-red-800';
      case OrderStatusType.CANCELLED:
        return 'bg-gray-100 text-gray-800';
      case OrderStatusType.REFUNDED:
        return 'bg-orange-100 text-orange-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const formatDate = (timestamp: any) => {
    if (!timestamp) return 'N/A';
    const date = timestamp.toDate ? timestamp.toDate() : new Date(timestamp);
    return date.toLocaleDateString() + ' ' + date.toLocaleTimeString();
  };

  const formatPrice = (price: string | number) => {
    const numPrice = typeof price === 'string' ? parseFloat(price) : price;
    return numPrice.toFixed(2);
  };

  if (!user) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-xl font-semibold mb-2">Please log in</h2>
          <p className="text-gray-600">You need to be logged in to view your orders.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">My Orders</h1>
          <p className="mt-2 text-gray-600">Track and manage your orders</p>
        </div>

        {/* Tab Navigation */}
        <div className="mb-6">
          <nav className="flex space-x-8">
            <button
              onClick={() => setActiveTab('my-orders')}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'my-orders'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              My Orders
            </button>
            <button
              onClick={() => setActiveTab('received-orders')}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'received-orders'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              Received Orders
            </button>
          </nav>
        </div>

        {loading ? (
          <div className="flex items-center justify-center py-12">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            <span className="ml-2">Loading orders...</span>
          </div>
        ) : error ? (
          <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">
            {error}
          </div>
        ) : orders.length === 0 ? (
          <div className="text-center py-12">
            <h3 className="text-lg font-medium text-gray-900 mb-2">No orders found</h3>
            <p className="text-gray-600">
              {activeTab === 'my-orders' 
                ? "You haven't placed any orders yet." 
                : "You haven't received any orders yet."}
            </p>
          </div>
        ) : (
          <div className="bg-white shadow overflow-hidden sm:rounded-md">
            <ul className="divide-y divide-gray-200">
              {orders.map((order) => (
                <li 
                  key={order.id} 
                  className={`px-6 py-4 ${
                    highlightOrderId === order.id 
                      ? 'bg-blue-50 border-l-4 border-blue-400' 
                      : 'hover:bg-gray-50'
                  }`}
                >
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      <div className="flex items-center justify-between">
                        <div>
                          <h3 className="text-lg font-medium text-gray-900">
                            {order.serviceModel?.title || 'Service Order'}
                          </h3>
                          <p className="text-sm text-gray-600">
                            Order #{order.uniqueId || order.id}
                          </p>
                        </div>
                        <div className="flex items-center space-x-4">
                          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(order.status)}`}>
                            {order.status}
                          </span>
                          <div className="text-right">
                            <p className="text-lg font-semibold text-gray-900">
                              {order.currency?.toUpperCase() || 'USD'} {formatPrice(order.serviceModel?.price || '0')}
                            </p>
                            <p className="text-sm text-gray-600">
                              {formatDate(order.added_at)}
                            </p>
                          </div>
                        </div>
                      </div>
                      
                      {order.comment && (
                        <p className="mt-2 text-sm text-gray-600">
                          <span className="font-medium">Comment:</span> {order.comment}
                        </p>
                      )}
                      
                      {order.serviceModel?.description && (
                        <p className="mt-1 text-sm text-gray-600">
                          {order.serviceModel.description}
                        </p>
                      )}
                      
                      <div className="mt-2 flex items-center space-x-4 text-sm text-gray-500">
                        {order.transactionId && (
                          <span>Transaction: {order.transactionId}</span>
                        )}
                        {order.payment_intent_id && (
                          <span>Payment: {order.payment_intent_id.substring(0, 20)}...</span>
                        )}
                        {order.dueDate && (
                          <span>Due: {formatDate(order.dueDate)}</span>
                        )}
                      </div>
                    </div>
                  </div>
                  
                  {highlightOrderId === order.id && (
                    <div className="mt-3 p-3 bg-green-50 border border-green-200 rounded">
                      <div className="flex items-center">
                        <div className="flex-shrink-0">
                          <svg className="h-5 w-5 text-green-400" viewBox="0 0 20 20" fill="currentColor">
                            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                          </svg>
                        </div>
                        <div className="ml-3">
                          <p className="text-sm font-medium text-green-800">
                            Payment completed successfully!
                          </p>
                          <p className="text-sm text-green-700">
                            Your order has been created and is now being processed.
                          </p>
                        </div>
                      </div>
                    </div>
                  )}
                </li>
              ))}
            </ul>
          </div>
        )}
      </div>
    </div>
  );
}

export default function OrdersPage() {
  return (
    <Suspense fallback={
      <div className="min-h-screen bg-gray-50 py-8">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-gray-900">My Orders</h1>
            <p className="mt-2 text-gray-600">Loading your orders...</p>
          </div>
          <div className="flex items-center justify-center py-12">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            <span className="ml-2">Loading orders...</span>
          </div>
        </div>
      </div>
    }>
      <OrdersPageContent />
    </Suspense>
  );
}
