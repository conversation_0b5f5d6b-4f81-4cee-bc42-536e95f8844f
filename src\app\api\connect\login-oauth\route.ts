import { NextRequest, NextResponse } from 'next/server';
import { getUserIdFromRequest } from '@/lib/auth/serverAuth';
import { initFirebase } from '../../../../../firebaseConfig';
import { doc, getDoc } from 'firebase/firestore';

export async function POST(req: NextRequest) {
  try {
    // Get user ID from request
    const userId = await getUserIdFromRequest(req);
    if (!userId) {
      return NextResponse.json(
        {
          error: 'Authentication required. Please log in to access Stripe Connect OAuth.',
          code: 'AUTH_REQUIRED'
        },
        { status: 401 }
      );
    }

    // Parse request body for optional email, landing preference, and returnUrl
    let email: string | undefined;
    let landing: 'login' | 'register' = 'login';
    let returnUrl: string | undefined;
    try {
      const body = await req.json();
      email = body.email;
      if (body.landing === 'register') landing = 'register';
      if (typeof body.returnUrl === 'string' && body.returnUrl.length > 0) {
        returnUrl = body.returnUrl;
      }
    } catch {
      // Body parsing failed, continue with defaults
    }

    // If caller didn't provide returnUrl, default to embedded dashboard
    if (!returnUrl) {
      const originHdr = req.headers.get('origin') || 'http://localhost:3000';
      returnUrl = `${originHdr}/payment/connect-dashboard`;
    }

    // Determine landing automatically based on whether user already has stripe_id
    try {
      const { db } = await initFirebase();
      const userRef = doc(db, 'users', userId);
      const userSnap = await getDoc(userRef);
      const stripeId = userSnap.exists() ? (userSnap.data() as any)?.stripe_id : undefined;
      if (!stripeId) {
        landing = 'register'; // no connected account yet → show registration screen
      } else {
        landing = 'login';
      }

      // If no email provided, try to prefill from user doc
      if (!email && userSnap.exists()) {
        email = (userSnap.data() as any)?.email;
      }
    } catch (e) {
      // If Firebase fails, fall back to provided/default values
      console.warn('Could not check existing stripe_id, proceeding with defaults');
    }

    console.log('Creating Stripe Connect OAuth login for user:', userId, email ? `with email: ${email}` : '', `landing=${landing}`, returnUrl ? `returnUrl=${returnUrl}` : '');

    // Get the origin for redirect URLs
    const origin = req.headers.get('origin') || 'http://localhost:3000';

    // Stripe Connect OAuth parameters
    const clientId = process.env.STRIPE_CONNECT_CLIENT_ID;

    if (!clientId) {
      return NextResponse.json(
        { error: 'Stripe Connect not configured. Please set STRIPE_CONNECT_CLIENT_ID in environment variables.' },
        { status: 500 }
      );
    }

    // Build OAuth URL for Stripe Connect
    const oauthParams = new URLSearchParams({
      response_type: 'code',
      client_id: clientId,
      scope: 'read_write',
      redirect_uri: `${origin}/api/connect/oauth-callback`,
      // Pack userId and returnUrl (if any) into state as JSON
      state: JSON.stringify({ userId, returnUrl }),
      // 'login' focuses on existing users; 'register' goes to the email-first onboarding screen
      'stripe_landing': landing === 'register' ? 'register' : 'login'
    });

    // Add email parameter if provided (prefills the email field on Stripe)
    if (email && email.trim()) {
      oauthParams.set('stripe_user[email]', email.trim());
    }

    const oauthUrl = `https://connect.stripe.com/oauth/authorize?${oauthParams.toString()}`;

    return NextResponse.json({
      success: true,
      message: 'OAuth login URL generated successfully',
      loginUrl: oauthUrl,
      userId,
      email: email || null,
      instructions: 'Open the loginUrl to authenticate with your existing Stripe account',
      popupFeatures: 'width=600,height=700,scrollbars=yes,resizable=yes,status=yes,location=yes'
    });

  } catch (error) {
    console.error('Error creating OAuth login:', error);
    return NextResponse.json(
      { error: 'Failed to create login URL' },
      { status: 500 }
    );
  }
}
