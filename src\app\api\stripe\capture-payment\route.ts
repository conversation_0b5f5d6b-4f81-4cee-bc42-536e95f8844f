import { NextRequest, NextResponse } from 'next/server';
import { Stripe } from 'stripe';

if (!process.env.STRIPE_SECRET_KEY) {
  throw new Error("STRIPE_SECRET_KEY is not defined");
}

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY);

export async function POST(request: NextRequest) {
  try {
    const { paymentIntentId, chargeId, isUS = false } = await request.json();

    if (!paymentIntentId && !chargeId) {
      return NextResponse.json({
        success: false,
        error: 'Payment Intent ID or Charge ID is required'
      }, { status: 400 });
    }

    console.log('💳 ===== CAPTURING PAYMENT =====');
    console.log(`📋 Payment Intent ID: ${paymentIntentId}`);
    console.log(`🔗 Charge ID: ${chargeId}`);
    console.log(`🌍 Using US Stripe: ${isUS}`);
    console.log('🕐 Timestamp:', new Date().toISOString());

    let targetPaymentIntentId = paymentIntentId;

    // If only charge ID provided, get payment intent from charge
    if (!paymentIntentId && chargeId) {
      console.log('🔄 Getting payment intent from charge...');
      const charge = await stripe.charges.retrieve(chargeId);
      targetPaymentIntentId = charge.payment_intent as string;
      console.log(`✅ Found payment intent: ${targetPaymentIntentId}`);
    }

    // Get current payment intent status
    console.log('🔄 Checking current payment intent status...');
    const paymentIntent = await stripe.paymentIntents.retrieve(targetPaymentIntentId);
    
    console.log('📊 Current Payment Intent Status:', {
      id: paymentIntent.id,
      status: paymentIntent.status,
      amount: paymentIntent.amount,
      amount_capturable: paymentIntent.amount_capturable,
      amount_received: paymentIntent.amount_received,
      capture_method: paymentIntent.capture_method
    });

    if (paymentIntent.status === 'succeeded') {
      console.log('✅ Payment already captured and succeeded');
      return NextResponse.json({
        success: true,
        message: 'Payment already captured',
        paymentIntent: {
          id: paymentIntent.id,
          status: paymentIntent.status,
          amount: paymentIntent.amount,
          amount_received: paymentIntent.amount_received,
          currency: paymentIntent.currency
        },
        alreadyCaptured: true
      });
    }

    if (paymentIntent.status !== 'requires_capture') {
      return NextResponse.json({
        success: false,
        error: `Cannot capture payment. Current status: ${paymentIntent.status}`,
        details: 'Payment must be in requires_capture status to be captured',
        paymentIntent: {
          id: paymentIntent.id,
          status: paymentIntent.status,
          amount: paymentIntent.amount
        }
      }, { status: 400 });
    }

    // Capture the full payment
    console.log('🔄 Capturing full payment...');
    const capturedPaymentIntent = await stripe.paymentIntents.capture(targetPaymentIntentId);

    console.log('✅ Payment captured successfully:', {
      id: capturedPaymentIntent.id,
      status: capturedPaymentIntent.status,
      amount: capturedPaymentIntent.amount,
      amount_received: capturedPaymentIntent.amount_received,
      currency: capturedPaymentIntent.currency
    });

    // Get updated charge information
    console.log('🔄 Getting updated charge information...');
    const charges = await stripe.charges.list({
      payment_intent: targetPaymentIntentId,
      limit: 1
    });

    const latestCharge = charges.data[0];
    console.log('✅ Updated charge status:', {
      id: latestCharge?.id,
      status: latestCharge?.status,
      captured: latestCharge?.captured,
      amount_captured: latestCharge?.amount_captured
    });

    const responseData = {
      success: true,
      message: 'Payment captured successfully - Status changed from Uncaptured to Succeeded',
      paymentIntent: {
        id: capturedPaymentIntent.id,
        status: capturedPaymentIntent.status,
        amount: capturedPaymentIntent.amount,
        amount_received: capturedPaymentIntent.amount_received,
        currency: capturedPaymentIntent.currency,
        amountFormatted: `$${(capturedPaymentIntent.amount / 100).toFixed(2)}`
      },
      charge: latestCharge ? {
        id: latestCharge.id,
        status: latestCharge.status,
        captured: latestCharge.captured,
        amount_captured: latestCharge.amount_captured,
        amountCapturedFormatted: `$${(latestCharge.amount_captured / 100).toFixed(2)}`
      } : null,
      statusChange: {
        from: 'Uncaptured',
        to: 'Succeeded'
      },
      timestamp: new Date().toISOString()
    };

    console.log('✅ ===== PAYMENT CAPTURE COMPLETED =====');
    console.log(`💳 Payment Intent: ${capturedPaymentIntent.id}`);
    console.log(`📊 Status: ${capturedPaymentIntent.status}`);
    console.log(`💰 Amount Captured: $${(capturedPaymentIntent.amount_received / 100).toFixed(2)}`);
    console.log(`🔗 Charge ID: ${latestCharge?.id}`);
    console.log('🔚 ===== END PAYMENT CAPTURE =====');

    return NextResponse.json(responseData);

  } catch (error) {
    console.error('❌ Error capturing payment:', error);
    
    if (error instanceof Stripe.errors.StripeError) {
      return NextResponse.json({
        success: false,
        error: 'Stripe API error',
        details: error.message,
        type: error.type
      }, { status: 400 });
    }

    return NextResponse.json({
      success: false,
      error: 'Failed to capture payment',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

// GET method for convenience
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const paymentIntentId = searchParams.get('payment_intent_id');
    const chargeId = searchParams.get('charge_id');
    const isUS = searchParams.get('isUS') === 'true';

    if (!paymentIntentId && !chargeId) {
      return NextResponse.json({
        success: false,
        error: 'Payment Intent ID or Charge ID is required'
      }, { status: 400 });
    }

    // Convert to POST request format
    const postRequest = new NextRequest(request.url, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ paymentIntentId, chargeId, isUS })
    });

    return POST(postRequest);

  } catch (error) {
    console.error('❌ Error in GET capture payment:', error);
    return NextResponse.json({
      success: false,
      error: 'Failed to capture payment',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
