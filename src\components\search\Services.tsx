import React, { useEffect, useState, useRef, useCallback } from "react";
import { FilterSearchManager, ServiceSearchBy } from "@/services/filtersServices";
import { GlobalCard } from "@/globalComponents/globalCard";
import EmptyState from "@/components/EmptyState";
import { themes } from "../../../theme";
import { useRouter } from "next/navigation";
import Link from "next/link";
import { generateFileUrl } from "@/lib/utils";

const Services = ({
  searchFor,
  searchBy,
  search,
  SEARCH_FOR,
  SEARCH_BY_MAP,
  onClose,
  filters,
}: any) => {
  const [allServices, setAllServices] = useState<any[]>([]); // full list from backend
  const [visibleServices, setVisibleServices] = useState<any[]>([]); // paginated slice
  const [userLoading, setUserLoading] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const isLoadingMoreRef = useRef(false);
  const router = useRouter();
  const PAGE_SIZE = 50;


  // Fetch all services on search/searchBy change
  useEffect(() => {
    // console.log(filters);
    let filterBy;
    if (searchBy === "Service name" || searchBy === "Title") {
      filterBy = ServiceSearchBy.TITLE;
    } else if (searchBy === "Description") {
      filterBy = ServiceSearchBy.DESCRIPTION;
    } else {
      setAllServices([]);
      setVisibleServices([]);
      setHasMore(false);
      return;
    }
    setLoading(true);
    setError(null);
    isLoadingMoreRef.current = false; // Reset loading state
    FilterSearchManager.getInstance()
      .GetServiceByFilters({
        payload: {
          filterBy,
          searchTerm: search,
          limit: 5000, // get all possible results (adjust if needed)
          filters, // <-- pass filters here
        },
      })
      .then((resp) => {
        const filtered = Array.isArray(resp.services) ? resp.services : [];
        setAllServices(filtered);
        setVisibleServices(filtered.slice(0, PAGE_SIZE));
        setHasMore(filtered.length > PAGE_SIZE);
      })
      .catch((error) => {
        setError(error && error.message ? error.message : "Error fetching services");
        setAllServices([]);
        setVisibleServices([]);
        setHasMore(false);
      })
      .finally(() => setLoading(false));
  }, [search, searchBy, filters]);

  // Scroll handler for frontend-only pagination
  const handleScroll = useCallback(() => {
    const container = containerRef.current;
    if (!container || loading || !hasMore || isLoadingMoreRef.current) return;

    const { scrollTop, scrollHeight, clientHeight } = container;
    // Trigger load when user is within 100px of the bottom
    if (scrollHeight - scrollTop - clientHeight < 100) {
      // Load next page from allServices
      isLoadingMoreRef.current = true;
      setLoading(true);
      setTimeout(() => {
        setVisibleServices((prev) => {
          const next = allServices.slice(prev.length, prev.length + PAGE_SIZE);
          const updated = [...prev, ...next];
          setHasMore(updated.length < allServices.length);
          isLoadingMoreRef.current = false;
          setLoading(false);
          return updated;
        });
      }, 200); // simulate async
    }
  }, [loading, hasMore, allServices]);

  useEffect(() => {
    const container = containerRef.current;
    if (!container) return;
    container.addEventListener("scroll", handleScroll);
    return () => container.removeEventListener("scroll", handleScroll);
  }, [handleScroll]);

  // Generate public URL for Firebase Storage
  // const generateFileUrl = (postFile?: string) => {
  //   const baseUrl = process.env.BASE_STORAGE_URL;
  //   if (!postFile || !baseUrl) return undefined;
  //   if (postFile.startsWith("https://firebasestorage.googleapis.com/")) return postFile;
  //   return `${baseUrl}${encodeURIComponent(postFile)}?alt=media`;
  // };

  const hasServices = Array.isArray(visibleServices) && visibleServices.length > 0;

  return (
    <div ref={containerRef} className="overflow-y-auto h-[calc(100vh-200px)] min-h-0">
      {hasServices ? (
        <div className="grid grid-cols-4 max-md:grid-cols-1 max-lg:grid-cols-2 gap-3 mt-4">
          {visibleServices
            .filter(
              (item: any) =>
                (item.category !== "Customer" && item.profile_name) || item.profile_name
            )
            .map((item: any) => (
              <div key={item.id}>
                {Object.entries(themes).map(([_, innerThemeProperties]) => (
                  <div key={innerThemeProperties.title}>
                    {(item.category === "Storytelling" ? "Literature" : item.category) ===
                      innerThemeProperties.title && (
                      <div>
                        <Link
                          href={`/profile/amuzn/${item?.profile_name?.replace(/\s+/g, "-")}`}
                          className="w-full text-left row gap-2 my-2"
                          style={{ opacity: userLoading === item.id ? 0.6 : 1 }}
                          onClick={onClose}
                        >
                          <img
                            src={
                              item?.profile_pic
                                ? generateFileUrl(item?.profile_pic)
                                : "/assets/profileAvatar.svg"
                            }
                            alt=""
                            className="w-[40px] h-[40px] min-h-[40px] min-w-[40px] rounded-full object-cover"
                            style={{
                              border: "3px solid",
                              borderColor: innerThemeProperties.backgroundColor || "#000",
                            }}
                          />
                          <p className=" line-clamp-1 font-bold font-sf">
                            {item?.profile_name || "Profile Name"}
                          </p>
                        </Link>
                        <Link
                          href={
                            `/profile/amuzn/${item?.profile_name?.replace(/\s+/g, "-")}` +
                            "?view=Services"
                          }
                          className="w-full text-left"
                          style={{ opacity: userLoading === item.id ? 0.6 : 1 }}
                          onClick={onClose}
                        >
                          <GlobalCard
                            key={item.id}
                            border={innerThemeProperties.backgroundColor}
                            title={item.title || "Untitled Service"}
                            duration={item.duration}
                            description={item.description || "No description available"}
                            price={item.price}
                            currency={item.currency || "GBP"}
                          />
                        </Link>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            ))}
        </div>
      ) : loading ? (
        <div className="flex justify-center items-center h-full py-8">
          <span className="loader">Loading...</span>
        </div>
      ) : (
        <div className="flex justify-center items-center h-full">
          <EmptyState
            type="services"
            title="No services found"
            message="Try adjusting your search or filter to find services."
          />
        </div>
      )}
      {/* Show loading spinner at bottom if loading more */}
      {hasServices && loading && (
        <div className="flex justify-center items-center py-4">
          <span className="loader">Loading...</span>
        </div>
      )}
      {/* Show 'No more data found' if all data loaded */}
      {hasServices && !hasMore && !loading && (
        <div className="flex justify-center items-center py-4 text-gray-500">
          No more data found
        </div>
      )}
      {/* Show error if any */}
      {error && <div className="text-red-500 text-center py-2">{error}</div>}
    </div>
  );
};

export default Services;
