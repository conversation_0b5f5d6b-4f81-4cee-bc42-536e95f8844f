"use client";

import React, { useState, useEffect } from 'react';
import { useCurrentUser } from '@/hooks/useCurrentUser';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Ta<PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  CheckCircle, 
  AlertCircle, 
  Loader2, 
  TestTube,
  User,
  CreditCard,
  Building,
  Settings
} from 'lucide-react';

interface TestResult {
  name: string;
  status: 'pending' | 'running' | 'success' | 'error';
  message?: string;
  data?: any;
}

export default function ConnectTestPage() {
  const { user, loading: userLoading } = useCurrentUser();
  const [tests, setTests] = useState<TestResult[]>([
    { name: 'Account Session Creation', status: 'pending' },
    { name: 'Connect Instance Initialization', status: 'pending' },
    { name: 'Account Onboarding Component', status: 'pending' },
    { name: 'Payment Intent with Connect', status: 'pending' },
    { name: 'Seller Account Retrieval', status: 'pending' },
  ]);
  const [testAccountId, setTestAccountId] = useState('acct_1QSJJhRqnAbyhXpf');
  const [isRunning, setIsRunning] = useState(false);

  const updateTestStatus = (testName: string, status: TestResult['status'], message?: string, data?: any) => {
    setTests(prev => prev.map(test => 
      test.name === testName 
        ? { ...test, status, message, data }
        : test
    ));
  };

  const runTests = async () => {
    setIsRunning(true);
    
    // Reset all tests
    setTests(prev => prev.map(test => ({ ...test, status: 'pending' })));

    try {
      // Test 1: Account Session Creation
      updateTestStatus('Account Session Creation', 'running');
      try {
        const sessionResponse = await fetch('/api/connect/account-session', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            accountId: testAccountId,
            components: ['account_onboarding', 'account_management']
          })
        });
        
        const sessionData = await sessionResponse.json();
        
        if (sessionData.success && sessionData.clientSecret) {
          updateTestStatus('Account Session Creation', 'success', 
            'Account session created successfully', sessionData);
        } else {
          updateTestStatus('Account Session Creation', 'error', 
            sessionData.error || 'Failed to create account session');
        }
      } catch (error) {
        updateTestStatus('Account Session Creation', 'error', 
          `Error: ${error instanceof Error ? error.message : 'Unknown error'}`);
      }

      // Test 2: Connect Instance Initialization
      updateTestStatus('Connect Instance Initialization', 'running');
      try {
        // Test if Connect.js can be loaded
        const connectModule = await import('@stripe/connect-js');
        if (connectModule.loadConnectAndInitialize) {
          updateTestStatus('Connect Instance Initialization', 'success', 
            'Connect.js module loaded successfully');
        } else {
          updateTestStatus('Connect Instance Initialization', 'error', 
            'Connect.js module missing required functions');
        }
      } catch (error) {
        updateTestStatus('Connect Instance Initialization', 'error', 
          `Error loading Connect.js: ${error instanceof Error ? error.message : 'Unknown error'}`);
      }

      // Test 3: Account Onboarding Component
      updateTestStatus('Account Onboarding Component', 'running');
      try {
        // Test if React Connect components can be loaded
        const reactConnectModule = await import('@stripe/react-connect-js');
        if (reactConnectModule.ConnectAccountOnboarding) {
          updateTestStatus('Account Onboarding Component', 'success', 
            'React Connect components loaded successfully');
        } else {
          updateTestStatus('Account Onboarding Component', 'error', 
            'React Connect components missing required exports');
        }
      } catch (error) {
        updateTestStatus('Account Onboarding Component', 'error', 
          `Error loading React Connect: ${error instanceof Error ? error.message : 'Unknown error'}`);
      }

      // Test 4: Payment Intent with Connect
      updateTestStatus('Payment Intent with Connect', 'running');
      try {
        const paymentResponse = await fetch('/api/create-payment-intent', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            amount: 1000,
            currency: 'usd',
            productName: 'Test Product',
            userId: user?.uid || 'test_user',
            sellerAccountId: testAccountId,
            userEmail: user?.email || '<EMAIL>'
          })
        });
        
        const paymentData = await paymentResponse.json();
        
        if (paymentData.success || paymentData.client_secret) {
          updateTestStatus('Payment Intent with Connect', 'success', 
            'Payment intent created successfully', paymentData);
        } else {
          updateTestStatus('Payment Intent with Connect', 'error', 
            paymentData.error || 'Failed to create payment intent');
        }
      } catch (error) {
        updateTestStatus('Payment Intent with Connect', 'error', 
          `Error: ${error instanceof Error ? error.message : 'Unknown error'}`);
      }

      // Test 5: Seller Account Retrieval
      updateTestStatus('Seller Account Retrieval', 'running');
      try {
        if (user?.uid) {
          const sellerResponse = await fetch(`/api/sellers/${user.uid}`);
          const sellerData = await sellerResponse.json();
          
          if (sellerResponse.ok) {
            updateTestStatus('Seller Account Retrieval', 'success', 
              'Seller account retrieved successfully', sellerData);
          } else if (sellerResponse.status === 404) {
            updateTestStatus('Seller Account Retrieval', 'success', 
              'No seller account found (expected for new users)');
          } else {
            updateTestStatus('Seller Account Retrieval', 'error', 
              sellerData.error || 'Failed to retrieve seller account');
          }
        } else {
          updateTestStatus('Seller Account Retrieval', 'error', 
            'No authenticated user found');
        }
      } catch (error) {
        updateTestStatus('Seller Account Retrieval', 'error', 
          `Error: ${error instanceof Error ? error.message : 'Unknown error'}`);
      }

    } finally {
      setIsRunning(false);
    }
  };

  const getStatusIcon = (status: TestResult['status']) => {
    switch (status) {
      case 'running':
        return <Loader2 className="h-4 w-4 animate-spin text-blue-500" />;
      case 'success':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'error':
        return <AlertCircle className="h-4 w-4 text-red-500" />;
      default:
        return <div className="h-4 w-4 rounded-full border-2 border-gray-300" />;
    }
  };

  const getStatusColor = (status: TestResult['status']) => {
    switch (status) {
      case 'running':
        return 'border-blue-200 bg-blue-50';
      case 'success':
        return 'border-green-200 bg-green-50';
      case 'error':
        return 'border-red-200 bg-red-50';
      default:
        return 'border-gray-200 bg-gray-50';
    }
  };

  if (userLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
          <p className="text-gray-600">Loading...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto px-4 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2 flex items-center">
            <TestTube className="h-8 w-8 mr-3" />
            Connect Embedded Components Test Suite
          </h1>
          <p className="text-gray-600">
            Validate that all Connect embedded components are working correctly
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Test Configuration */}
          <div className="lg:col-span-1">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Settings className="h-5 w-5 mr-2" />
                  Test Configuration
                </CardTitle>
                <CardDescription>
                  Configure test parameters
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="accountId">Test Account ID</Label>
                  <Input
                    id="accountId"
                    value={testAccountId}
                    onChange={(e) => setTestAccountId(e.target.value)}
                    placeholder="acct_..."
                  />
                </div>

                {user && (
                  <div className="bg-blue-50 p-3 rounded-lg">
                    <p className="text-sm text-blue-800">
                      <strong>User:</strong> {user.email}
                    </p>
                    <p className="text-xs text-blue-600">
                      ID: {user.uid}
                    </p>
                  </div>
                )}

                <Button 
                  onClick={runTests} 
                  disabled={isRunning}
                  className="w-full"
                >
                  {isRunning ? (
                    <>
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      Running Tests...
                    </>
                  ) : (
                    'Run All Tests'
                  )}
                </Button>
              </CardContent>
            </Card>
          </div>

          {/* Test Results */}
          <div className="lg:col-span-2">
            <Card>
              <CardHeader>
                <CardTitle>Test Results</CardTitle>
                <CardDescription>
                  Status of Connect embedded components tests
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {tests.map((test, index) => (
                    <div 
                      key={test.name}
                      className={`p-4 rounded-lg border ${getStatusColor(test.status)}`}
                    >
                      <div className="flex items-center justify-between mb-2">
                        <h4 className="font-medium">{test.name}</h4>
                        {getStatusIcon(test.status)}
                      </div>
                      
                      {test.message && (
                        <p className="text-sm text-gray-600 mb-2">
                          {test.message}
                        </p>
                      )}
                      
                      {test.data && (
                        <details className="text-xs">
                          <summary className="cursor-pointer text-gray-500 hover:text-gray-700">
                            View Details
                          </summary>
                          <pre className="mt-2 p-2 bg-white rounded border overflow-x-auto">
                            {JSON.stringify(test.data, null, 2)}
                          </pre>
                        </details>
                      )}
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Quick Links */}
        <Card className="mt-6">
          <CardHeader>
            <CardTitle>Quick Links</CardTitle>
            <CardDescription>
              Navigate to different Connect components
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <Button 
                variant="outline" 
                onClick={() => window.location.href = '/payment/connect-dashboard'}
                className="flex items-center"
              >
                <Building className="h-4 w-4 mr-2" />
                Dashboard
              </Button>
              <Button 
                variant="outline" 
                onClick={() => window.location.href = '/payment/seller-onboard'}
                className="flex items-center"
              >
                <User className="h-4 w-4 mr-2" />
                Onboarding
              </Button>
              <Button 
                variant="outline" 
                onClick={() => window.location.href = '/payment/connect-demo'}
                className="flex items-center"
              >
                <TestTube className="h-4 w-4 mr-2" />
                Demo
              </Button>
              <Button 
                variant="outline" 
                onClick={() => window.location.href = '/payment'}
                className="flex items-center"
              >
                <CreditCard className="h-4 w-4 mr-2" />
                API Tester
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
