"use client";

import { useState } from 'react';
import { Elements } from '@stripe/react-stripe-js';
import { loadStripe } from '@stripe/stripe-js';
import SimpleEnhancedCheckout from '@/components/SimpleEnhancedCheckout';
import { getCurrency, setCurrency, getCurrencySymbol, getUserData } from '@/lib/simpleCurrency';

const stripePromise = loadStripe(process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY!);

export default function SimplePaymentDemo() {
  const [amount, setAmount] = useState(20); // $20.00
  const [showCheckout, setShowCheckout] = useState(false);
  const [currency, setCurrencyState] = useState(getCurrency());
  const [enableApplePay, setEnableApplePay] = useState(true);
  const [enableGooglePay, setEnableGooglePay] = useState(true);
  const [isEscrow, setIsEscrow] = useState(false);

  const user = getUserData();

  const handlePaymentSuccess = (paymentIntent: any) => {
    console.log('Payment successful:', paymentIntent);
    alert(`Payment successful! ID: ${paymentIntent.id}`);
    setShowCheckout(false);
  };

  const handlePaymentError = (error: string) => {
    console.error('Payment error:', error);
    alert(`Payment failed: ${error}`);
  };

  const handleCurrencyChange = (newCurrency: string) => {
    setCurrency(newCurrency);
    setCurrencyState(newCurrency);
  };

  if (showCheckout) {
    return (
      <div className="max-w-md mx-auto mt-8 p-4">
        <h1 className="text-2xl font-bold mb-6">Simple Enhanced Payment</h1>
        
        <Elements stripe={stripePromise}>
          <SimpleEnhancedCheckout
            amount={amount * 100} // Convert to cents
            currency={currency}
            productName="Demo Product"
            userId={user?.uid}
            isEscrow={isEscrow}
            enableApplePay={enableApplePay}
            enableGooglePay={enableGooglePay}
            onSuccess={handlePaymentSuccess}
            onError={handlePaymentError}
          />
        </Elements>
        
        <button 
          onClick={() => setShowCheckout(false)}
          className="mt-4 text-gray-600 hover:text-gray-800"
        >
          ← Back
        </button>
      </div>
    );
  }

  return (
    <div className="max-w-md mx-auto mt-8 p-4">
      <h1 className="text-2xl font-bold mb-6">Simple Enhanced Payment Demo</h1>
      
      {/* User Info */}
      {user && (
        <div className="mb-6 p-3 bg-gray-100 rounded">
          <h3 className="font-semibold mb-2">User Info</h3>
          <p className="text-sm">Email: {user.email}</p>
          <p className="text-sm">Name: {user.displayName || 'Not set'}</p>
        </div>
      )}

      {/* Settings */}
      <div className="space-y-4 mb-6">
        {/* Amount */}
        <div>
          <label className="block text-sm font-medium mb-1">
            Amount ({getCurrencySymbol(currency)})
          </label>
          <input
            type="number"
            value={amount}
            onChange={(e) => setAmount(parseFloat(e.target.value) || 0)}
            className="w-full p-2 border rounded"
            min="1"
            step="0.01"
          />
        </div>

        {/* Currency */}
        <div>
          <label className="block text-sm font-medium mb-1">Currency</label>
          <select
            value={currency}
            onChange={(e) => handleCurrencyChange(e.target.value)}
            className="w-full p-2 border rounded"
          >
            <option value="USD">USD - US Dollar</option>
            <option value="EUR">EUR - Euro</option>
            <option value="GBP">GBP - British Pound</option>
            <option value="CAD">CAD - Canadian Dollar</option>
            <option value="AUD">AUD - Australian Dollar</option>
            <option value="JPY">JPY - Japanese Yen</option>
            <option value="CHF">CHF - Swiss Franc</option>
            <option value="SEK">SEK - Swedish Krona</option>
            <option value="NOK">NOK - Norwegian Krone</option>
            <option value="DKK">DKK - Danish Krone</option>
          </select>
        </div>

        {/* Payment Methods */}
        <div>
          <label className="block text-sm font-medium mb-2">Payment Methods</label>
          <div className="space-y-2">
            <label className="flex items-center">
              <input
                type="checkbox"
                checked={enableApplePay}
                onChange={(e) => setEnableApplePay(e.target.checked)}
                className="mr-2"
              />
              <span className="text-sm">Enable Apple Pay</span>
            </label>
            <label className="flex items-center">
              <input
                type="checkbox"
                checked={enableGooglePay}
                onChange={(e) => setEnableGooglePay(e.target.checked)}
                className="mr-2"
              />
              <span className="text-sm">Enable Google Pay</span>
            </label>
          </div>
        </div>

        {/* Escrow */}
        <div>
          <label className="flex items-center">
            <input
              type="checkbox"
              checked={isEscrow}
              onChange={(e) => setIsEscrow(e.target.checked)}
              className="mr-2"
            />
            <span className="text-sm">Escrow Payment (manual capture)</span>
          </label>
        </div>
      </div>

      {/* Start Payment */}
      <button 
        onClick={() => setShowCheckout(true)}
        className="w-full bg-black text-white font-bold py-3 px-4 rounded hover:bg-gray-800"
      >
        Pay {getCurrencySymbol(currency)}{amount.toFixed(2)}
      </button>

      {/* Features */}
      <div className="mt-6 p-3 bg-blue-50 rounded">
        <h3 className="font-semibold mb-2">Features</h3>
        <ul className="text-sm space-y-1">
          <li>✅ Dynamic currency from localStorage</li>
          <li>✅ Apple Pay & Google Pay support</li>
          <li>✅ User data from localStorage</li>
          <li>✅ Simple and clean implementation</li>
          <li>✅ Escrow payment support</li>
        </ul>
      </div>
    </div>
  );
}
