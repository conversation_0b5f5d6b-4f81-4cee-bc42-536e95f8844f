"use client";

import React, { useState } from 'react';
import { useCurrentUser } from '@/hooks/useCurrentUser';

interface CryptoPaymentFormProps {
  amount: number;
  currency: string;
  productName: string;
  orderId: string;
  userId?: string;
  sellerId: string;
  onSuccess?: (chargeData: any) => void;
  onError?: (error: string) => void;
}

export default function CryptoPaymentForm({
  amount,
  currency,
  productName,
  orderId,
  userId,
  sellerId,
  onSuccess,
  onError
}: CryptoPaymentFormProps) {
  const { user } = useCurrentUser();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleCryptoPayment = async () => {
    setIsLoading(true);
    setError(null);

    try {
      console.log('🪙 Creating crypto payment charge...');

      const response = await fetch('/api/coinbase/create-charge', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          amount,
          currency,
          productName,
          orderId,
          userId: userId || user?.uid,
          sellerId,
          userEmail: user?.email,
          userName: user?.displayName || user?.email
        }),
      });

      const data = await response.json();

      if (!data.success) {
        throw new Error(data.error || 'Failed to create crypto payment');
      }

      console.log('✅ Crypto charge created:', data);

      // Redirect to Coinbase Commerce hosted page
      window.location.href = data.hostedUrl;

      // Call success callback
      onSuccess?.(data);

    } catch (err) {
      console.error('❌ Error creating crypto payment:', err);
      const errorMessage = err instanceof Error ? err.message : 'Failed to create crypto payment';
      setError(errorMessage);
      onError?.(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="max-w-md mx-auto bg-white p-6 rounded-lg shadow-sm border">
      <div className="mb-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-2">
          Pay with Cryptocurrency
        </h3>
        <div className="flex justify-between items-center">
          <span className="text-gray-600">Amount:</span>
          <p className="text-lg font-medium">
            {currency.toUpperCase()} {(amount / 100).toFixed(2)}
          </p>
        </div>
        <p className="text-sm text-blue-600 mt-2">
          🪙 Secure crypto payment via Coinbase Commerce
        </p>
      </div>

      {error && (
        <div className="mb-4 p-3 bg-red-50 border border-red-200 text-red-700 rounded-lg">
          <p className="text-sm">{error}</p>
        </div>
      )}

      <div className="space-y-4">
        <div className="bg-gray-50 p-4 rounded-lg">
          <h4 className="font-medium text-gray-900 mb-2">Supported Cryptocurrencies:</h4>
          <div className="grid grid-cols-2 gap-2 text-sm text-gray-600">
            <div className="flex items-center">
              <span className="mr-2">₿</span> Bitcoin (BTC)
            </div>
            <div className="flex items-center">
              <span className="mr-2">Ξ</span> Ethereum (ETH)
            </div>
            <div className="flex items-center">
              <span className="mr-2">Ł</span> Litecoin (LTC)
            </div>
            <div className="flex items-center">
              <span className="mr-2">₿</span> Bitcoin Cash (BCH)
            </div>
          </div>
        </div>

        <button
          onClick={handleCryptoPayment}
          disabled={isLoading}
          className={`w-full py-3 px-4 rounded-lg font-medium transition-colors ${
            isLoading
              ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
              : 'bg-orange-500 hover:bg-orange-600 text-white'
          }`}
        >
          {isLoading ? (
            <div className="flex items-center justify-center">
              <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
              Creating Payment...
            </div>
          ) : (
            <div className="flex items-center justify-center">
              <span className="mr-2">🪙</span>
              Pay with Crypto
            </div>
          )}
        </button>

        <div className="text-xs text-gray-500 text-center">
          <p>You'll be redirected to Coinbase Commerce to complete your payment.</p>
          <p className="mt-1">Payments are processed securely and converted to fiat currency.</p>
        </div>
      </div>
    </div>
  );
}
