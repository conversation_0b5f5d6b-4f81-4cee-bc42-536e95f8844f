"use client";

import React, { useState, useEffect } from 'react';
import { ConnectEmbeddedProvider } from './ConnectEmbeddedProvider';
import ConnectAccountOnboardingWrapper from './ConnectAccountOnboarding';
import ConnectAccountManagementWrapper, { ConnectPayoutsWrapper } from './ConnectAccountManagement';
import ConnectPaymentsWrapper from './ConnectPayments';
import { useCurrentUser } from '@/hooks/useCurrentUser';
import { Button } from './ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from './ui/tabs';
import { 
  CreditCard, 
  Settings, 
  DollarSign, 
  User, 
  AlertCircle,
  CheckCircle,
  Loader2 
} from 'lucide-react';

interface ConnectDashboardProps {
  className?: string;
}

interface SellerAccount {
  stripeAccountId: string;
  onboardingComplete: boolean;
  chargesEnabled: boolean;
  payoutsEnabled: boolean;
  email?: string;
  businessName?: string;
}

export const ConnectDashboard: React.FC<ConnectDashboardProps> = ({
  className = "",
}) => {
  const { user } = useCurrentUser();
  const [sellerAccount, setSellerAccount] = useState<SellerAccount | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState('overview');

  // Fetch seller account information
  useEffect(() => {
    const fetchSellerAccount = async () => {
      if (!user?.uid) {
        setIsLoading(false);
        return;
      }

      try {
        const response = await fetch(`/api/sellers/${user.uid}`);
        if (response.ok) {
          const data = await response.json();
          setSellerAccount(data);
        } else if (response.status === 404) {
          // No seller account exists yet
          setSellerAccount(null);
        } else {
          throw new Error('Failed to fetch seller account');
        }
      } catch (err) {
        console.error('Error fetching seller account:', err);
        setError(err instanceof Error ? err.message : 'Failed to load account');
      } finally {
        setIsLoading(false);
      }
    };

    fetchSellerAccount();
  }, [user?.uid]);

  const handleCreateAccount = async () => {
    if (!user?.uid || !user?.email) {
      setError('User information is required');
      return;
    }

    setIsLoading(true);
    try {
      const response = await fetch('/api/connect/onboard', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userId: user.uid,
          email: user.email,
          businessName: user.displayName || user.email,
        }),
      });

      const data = await response.json();
      if (data.error) {
        throw new Error(data.error);
      }

      // Refresh seller account data
      const accountResponse = await fetch(`/api/sellers/${user.uid}`);
      if (accountResponse.ok) {
        const accountData = await accountResponse.json();
        setSellerAccount(accountData);
        setActiveTab('onboarding');
      }
    } catch (err) {
      console.error('Error creating account:', err);
      setError(err instanceof Error ? err.message : 'Failed to create account');
    } finally {
      setIsLoading(false);
    }
  };

  const handleOnboardingComplete = (accountId: string) => {
    // Refresh account data after onboarding
    setSellerAccount(prev => prev ? {
      ...prev,
      onboardingComplete: true,
      chargesEnabled: true,
    } : null);
    setActiveTab('overview');
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
          <p className="text-gray-600">Loading your seller dashboard...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="text-center">
          <AlertCircle className="h-8 w-8 text-red-500 mx-auto mb-4" />
          <p className="text-red-600 mb-4">Error loading dashboard</p>
          <p className="text-sm text-gray-500 mb-4">{error}</p>
          <Button onClick={() => window.location.reload()}>
            Retry
          </Button>
        </div>
      </div>
    );
  }

  // No seller account - show creation flow
  if (!sellerAccount) {
    return (
      <div className={`connect-dashboard-container ${className}`}>
        <div className="max-w-2xl mx-auto text-center p-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-4">
            Become a Seller
          </h1>
          <p className="text-gray-600 mb-8">
            Start accepting payments by setting up your seller account. 
            This process is powered by Stripe Connect for secure and reliable payments.
          </p>
          
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-8">
            <h3 className="text-lg font-semibold text-blue-900 mb-2">
              What you'll need:
            </h3>
            <ul className="text-left text-blue-800 space-y-2">
              <li>• Business or personal information</li>
              <li>• Bank account details for payouts</li>
              <li>• Tax identification information</li>
              <li>• Valid government-issued ID</li>
            </ul>
          </div>

          <Button 
            onClick={handleCreateAccount}
            size="lg"
            className="w-full sm:w-auto"
          >
            Create Seller Account
          </Button>
        </div>
      </div>
    );
  }

  // Seller account exists - show dashboard
  return (
    <ConnectEmbeddedProvider 
      accountId={sellerAccount.stripeAccountId}
      components={['account_onboarding', 'account_management', 'payments', 'payouts', 'balances', 'notification_banner']}
    >
      <div className={`connect-dashboard-container ${className}`}>
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Seller Dashboard
          </h1>
          <div className="flex items-center space-x-4">
            <div className="flex items-center">
              {sellerAccount.onboardingComplete ? (
                <CheckCircle className="h-5 w-5 text-green-500 mr-2" />
              ) : (
                <AlertCircle className="h-5 w-5 text-yellow-500 mr-2" />
              )}
              <span className="text-sm text-gray-600">
                {sellerAccount.onboardingComplete ? 'Account Active' : 'Setup Required'}
              </span>
            </div>
            {sellerAccount.businessName && (
              <span className="text-sm text-gray-500">
                {sellerAccount.businessName}
              </span>
            )}
          </div>
        </div>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="overview" className="flex items-center">
              <User className="h-4 w-4 mr-2" />
              Overview
            </TabsTrigger>
            <TabsTrigger value="payments" className="flex items-center">
              <CreditCard className="h-4 w-4 mr-2" />
              Payments
            </TabsTrigger>
            <TabsTrigger value="payouts" className="flex items-center">
              <DollarSign className="h-4 w-4 mr-2" />
              Payouts
            </TabsTrigger>
            <TabsTrigger value="settings" className="flex items-center">
              <Settings className="h-4 w-4 mr-2" />
              Settings
            </TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="mt-6">
            {!sellerAccount.onboardingComplete ? (
              <ConnectAccountOnboardingWrapper
                onOnboardingComplete={handleOnboardingComplete}
                className="max-w-4xl mx-auto"
              />
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <div className="bg-white p-6 rounded-lg border">
                  <h3 className="text-lg font-semibold mb-2">Account Status</h3>
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span>Charges</span>
                      {sellerAccount.chargesEnabled ? (
                        <CheckCircle className="h-4 w-4 text-green-500" />
                      ) : (
                        <AlertCircle className="h-4 w-4 text-red-500" />
                      )}
                    </div>
                    <div className="flex items-center justify-between">
                      <span>Payouts</span>
                      {sellerAccount.payoutsEnabled ? (
                        <CheckCircle className="h-4 w-4 text-green-500" />
                      ) : (
                        <AlertCircle className="h-4 w-4 text-red-500" />
                      )}
                    </div>
                  </div>
                </div>
              </div>
            )}
          </TabsContent>

          <TabsContent value="payments" className="mt-6">
            <ConnectPaymentsWrapper
              accountId={sellerAccount.stripeAccountId}
              className="max-w-6xl mx-auto"
            />
          </TabsContent>

          <TabsContent value="payouts" className="mt-6">
            <ConnectPayoutsWrapper
              accountId={sellerAccount.stripeAccountId}
              className="max-w-6xl mx-auto"
            />
          </TabsContent>

          <TabsContent value="settings" className="mt-6">
            <ConnectAccountManagementWrapper
              accountId={sellerAccount.stripeAccountId}
              className="max-w-4xl mx-auto"
            />
          </TabsContent>
        </Tabs>
      </div>
    </ConnectEmbeddedProvider>
  );
};

export default ConnectDashboard;
