import { NextRequest, NextResponse } from "next/server";
import <PERSON><PERSON> from "stripe";
import { doc, setDoc } from "firebase/firestore";
import { initFirebase } from "../../../../../firebaseConfig";
import { UpdateUserAccountId } from "@/services/ordersServices";

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY as string);

export async function GET(req: NextRequest) {
  try {
    const { searchParams } = new URL(req.url);
    const code = searchParams.get("code");
    const stateRaw = searchParams.get("state"); // May contain JSON with userId and returnUrl

    // State can be a plain userId (legacy) or a JSON string
    let userId: string | null = null;
    let returnUrlFromState: string | null = null;
    if (stateRaw) {
      try {
        const parsed = JSON.parse(stateRaw);
        userId = parsed.userId || null;
        returnUrlFromState = parsed.returnUrl || null;
      } catch {
        // Legacy format: state just the userId
        userId = stateRaw;
      }
    }
    const error = searchParams.get("error");

    // Handle OAuth errors
    if (error) {
      const errorDescription =
        searchParams.get("error_description") || "OAuth authentication failed";
      console.error("OAuth error:", error, errorDescription);

      const origin = req.headers.get("origin") || "http://localhost:3000";
      return NextResponse.redirect(
        `${origin}/payment?error=${encodeURIComponent(errorDescription)}&tab=connect`
      );
    }

    if (!code || !userId) {
      return NextResponse.json(
        { error: "Missing authorization code or user state" },
        { status: 400 }
      );
    }

    console.log("Processing OAuth callback for user:", userId, "with code:", code);

    // Exchange authorization code for access token and account info
    const response = await stripe.oauth.token({
      grant_type: "authorization_code",
      code: code,
    });

    const {
      access_token,
      refresh_token,
      token_type,
      stripe_publishable_key,
      stripe_user_id,
      scope,
    } = response;

    console.log("OAuth token exchange successful for account:", stripe_user_id);

    if (!stripe_user_id) {
      return NextResponse.json({ error: "No Stripe account ID received" }, { status: 400 });
    }

    // Get detailed account information
    const account = await stripe.accounts.retrieve(stripe_user_id);

    // Save account details to stripeAccounts collection and update user
    try {
      const { db } = await initFirebase();

      // Save to stripeAccounts collection
      const stripeAccountRef = doc(db, "stripeAccounts", stripe_user_id);

      await setDoc(
        stripeAccountRef,
        {
          stripeAccountId: stripe_user_id,
          userId: userId,
          accessToken: access_token,
          refreshToken: refresh_token,
          publishableKey: stripe_publishable_key,
          tokenType: token_type,
          scope: scope,
          email: account.email,
          businessName: account.business_profile?.name,
          connectedAt: new Date().toISOString(),
          lastLoginAt: new Date().toISOString(),
          onboardingComplete: account?.details_submitted || false,
          chargesEnabled: account?.charges_enabled || false,
          payoutsEnabled: account?.payouts_enabled || false,
          country: account.country,
          currency: account.default_currency,
          accountType: account.type,
          businessType: account.business_type,
        },
        { merge: true }
      );

      // Update user collection with stripe_id
      try {
        // First, try to update directly using Firestore
        const userRef = doc(db, "users", userId);
        await setDoc(
          userRef,
          {
            stripe_id: stripe_user_id,
            updated_at: new Date().toISOString(),
          },
          { merge: true }
        );

        console.log("Successfully updated user with stripe_id via direct Firestore update");

        // Also try the service method as a backup
        try {
          const updateResult = await UpdateUserAccountId({
            user_id: userId,
            stripe_id: stripe_user_id,
          });

          if (updateResult.success) {
            console.log("Successfully updated user with stripe_id via service");
          }
        } catch (serviceError) {
          console.log("Service update failed, but direct update succeeded:", serviceError);
          // This is expected if the user is not authenticated, so we don't treat it as an error
        }
      } catch (userUpdateError) {
        console.error("Error updating user with stripe_id:", userUpdateError);
        // Continue with redirect even if user update fails
      }

      console.log("Saved OAuth account details to Firebase:", {
        userId,
        accountId: stripe_user_id,
        email: account.email,
      });
    } catch (firebaseError) {
      console.error("Error saving OAuth details to Firebase:", firebaseError);
      // Continue with redirect even if Firebase save fails
    }

    // Redirect back to returnUrl if provided; otherwise, home page
    const origin = req.headers.get("origin") || "http://localhost:3000";
    const baseReturn = returnUrlFromState || `${origin}/`;
    const redirectUrl = new URL(baseReturn, origin);
    redirectUrl.searchParams.set("connected", "true");
    redirectUrl.searchParams.set("account", stripe_user_id);
    redirectUrl.searchParams.set("userId", userId);
    redirectUrl.searchParams.set("tab", "connect");
    redirectUrl.searchParams.set("email", account.email || "");
    redirectUrl.searchParams.set("business", account.business_profile?.name || "");

    return NextResponse.redirect(redirectUrl.toString());
  } catch (error) {
    console.error("Error in OAuth callback:", error);

    const origin = req.headers.get("origin") || "http://localhost:3000";
    return NextResponse.redirect(
      `${origin}/payment?error=${encodeURIComponent("Failed to complete login")}&tab=connect`
    );
  }
}
