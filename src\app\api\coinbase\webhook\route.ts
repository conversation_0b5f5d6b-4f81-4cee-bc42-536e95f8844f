import { NextRequest, NextResponse } from 'next/server';
import { Webhook } from 'coinbase-commerce-node';
import { processPaymentSuccess, processPaymentFailure } from '@/services/postPaymentService';

export async function POST(request: NextRequest) {
  try {
    const body = await request.text();
    const signature = request.headers.get('x-cc-webhook-signature');

    if (!signature) {
      console.error('❌ Missing webhook signature');
      return NextResponse.json({ error: 'Missing signature' }, { status: 400 });
    }

    if (!process.env.COINBASE_COMMERCE_WEBHOOK_SECRET) {
      console.error('❌ Missing webhook secret');
      return NextResponse.json({ error: 'Webhook secret not configured' }, { status: 500 });
    }

    // Verify webhook signature
    let event;
    try {
      event = Webhook.verifyEventBody(body, signature, process.env.COINBASE_COMMERCE_WEBHOOK_SECRET);
    } catch (error) {
      console.error('❌ Invalid webhook signature:', error);
      return NextResponse.json({ error: 'Invalid signature' }, { status: 400 });
    }

    console.log('🪙 Coinbase webhook received:', {
      type: event.type,
      id: event.id,
      chargeId: event.data?.id
    });

    const charge = event.data;
    const metadata = charge?.metadata || {};

    // Extract order information from metadata
    const {
      orderId,
      userId,
      sellerId,
      userEmail,
      userName,
      isEscrow
    } = metadata;

    if (!orderId || !userId || !sellerId) {
      console.error('❌ Missing required metadata in webhook');
      return NextResponse.json({ error: 'Missing required metadata' }, { status: 400 });
    }

    // Handle different event types
    switch (event.type) {
      case 'charge:confirmed':
        console.log('✅ Crypto payment confirmed for order:', orderId);
        
        // Process successful payment
        await processPaymentSuccess({
          paymentIntentId: charge.id, // Use charge ID as payment intent ID
          orderId,
          transactionId: charge.code, // Use charge code as transaction ID
          amount: Math.round(parseFloat(charge.pricing.local.amount) * 100), // Convert to cents
          currency: charge.pricing.local.currency.toLowerCase(),
          isEscrow: isEscrow === 'true',
          userId,
          sellerId,
          userEmail,
          userName,
          sellerName: 'Seller' // You might want to fetch this from your seller data
        });

        break;

      case 'charge:failed':
        console.log('❌ Crypto payment failed for order:', orderId);
        
        // Process failed payment
        await processPaymentFailure({
          orderId,
          transactionId: charge.code,
          error: 'Crypto payment failed',
          userId,
          sellerId,
          userName,
          sellerName: 'Seller'
        });

        break;

      case 'charge:delayed':
        console.log('⏳ Crypto payment delayed for order:', orderId);
        // Handle delayed payment (waiting for blockchain confirmations)
        break;

      case 'charge:pending':
        console.log('⏳ Crypto payment pending for order:', orderId);
        // Handle pending payment
        break;

      case 'charge:resolved':
        console.log('✅ Crypto payment resolved for order:', orderId);
        // Handle resolved payment (after being delayed)
        break;

      default:
        console.log('ℹ️ Unhandled webhook event type:', event.type);
    }

    return NextResponse.json({ success: true });

  } catch (error) {
    console.error('❌ Error processing Coinbase webhook:', error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Webhook processing failed'
    }, { status: 500 });
  }
}
