"use client";
import React, { createContext, useContext, useEffect, useState, ReactNode } from "react";
import { User } from "@/services/UserInterface";
import { getUserById } from "@/services/usersServices";

// For ESLint
/* global localStorage */

interface AuthContextType {
  isLogin: boolean;
  userId: string;
  userData: User | null;
  users: User | null;
  isLoginLens: boolean;
  lensUserId: string;
  lensData: Record<string, unknown> | null;
  isLoading: boolean;
  refreshUserData: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [isLogin, setIsLogin] = useState(false);
  const [isLoginLens, setIsLoginLens] = useState(false);
  const [userId, setUserId] = useState("");
  const [lensUserId, setLensUserId] = useState("");
  const [users, setUsers] = useState<User | null>(null);
  const [userData, setUserData] = useState<User | null>(null);
  const [lensData, setLensData] = useState<Record<string, unknown> | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [hasInitialized, setHasInitialized] = useState(false);

  // Function to refresh user data from API
  const refreshUserData = async () => {
    const storedUser = localStorage.getItem("user");
    const user = storedUser ? JSON.parse(storedUser) : {};
    
    if (!user?.uid) return;

    try {
      console.log("Fetching user data for:", user.uid);
      
      const response = await getUserById(user.uid);
      
      if (response.success && response.user) {
        setUsers(response.user);
        
        // Update localStorage with fresh user data
        user.profile_name = response.user.profile_name;
        user.avatar = response.user.avatar;
        user.app_version = response.user.app_version;
        localStorage.setItem("user", JSON.stringify(user));
        setUserData(user);
      }
    } catch (error) {
      console.error("Error fetching user data:", error);
    }
  };

  // Initialize auth state from localStorage - only runs once
  useEffect(() => {
    if (hasInitialized) return;

    const initializeAuth = async () => {
      setIsLoading(true);
      
      try {
        // Parse user data from localStorage
        const storedUser = localStorage.getItem("user");
        const user = storedUser ? JSON.parse(storedUser) : {};
        
        // Parse lens user data from localStorage
        const storedLensUser = localStorage.getItem("lens-user");
        const lensUser = storedLensUser ? JSON.parse(storedLensUser) : {};

        // Set lens authentication state
        if (lensUser?.address) {
          setIsLoginLens(true);
          setLensUserId(lensUser.address);
          setLensData(lensUser);
        }

        // Set regular authentication state
        if (user?.uid) {
          setIsLogin(true);
          setUserId(user.uid);
          setUserData(user);
          
          // Fetch fresh user data from API - only once globally
          await refreshUserData();
        }
      } catch (error) {
        console.error("Error initializing auth:", error);
      } finally {
        setIsLoading(false);
        setHasInitialized(true);
      }
    };

    initializeAuth();
  }, [hasInitialized]);

  const value: AuthContextType = {
    isLogin,
    userId,
    userData,
    users,
    isLoginLens,
    lensUserId,
    lensData,
    isLoading,
    refreshUserData,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

// Custom hook to use the AuthContext
export const useAuthContext = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error("useAuthContext must be used within an AuthProvider");
  }
  return context;
};
