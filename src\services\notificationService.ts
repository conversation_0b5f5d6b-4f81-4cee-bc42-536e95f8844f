import {
  addDoc,
  collection,
  doc,
  getDoc,
  getDocs,
  increment,
  orderBy,
  query,
  serverTimestamp,
  setDoc,
  Timestamp,
  updateDoc,
  where,
  writeBatch,
} from "firebase/firestore";
import { initFirebase } from "../../firebaseConfig";
import { GATE_URL, getIdToken } from "@/lib/utils";

export enum NotificationEvents {
  COMMENT = "comment",
  REACTION = "reaction",
  BOOKMARK = "bookmark",
  FOLLOW = "follow",
  ORDER_STATUS_CHANGE= "order_status_change",

  // reminder to all followers
  POST_UPLOAD = "photo_upload",
  SERVICE_UPLOAD = "service_upload",
  EVENT_UPLOAD = "event_upload",
}

/**
 *  Photo Upload
 *
 *  {
 *      src_id:"user_1", // who has uploaded the post
 *      dest_id:"user_2" ,
 *      timestamp: // ,
 *      read:false,
 *      deleted:false,
 *
 *      event : "photo_upload" ,
 *      post_url:"http://firebase.io/aslkdnasd/asdasdkalsdkasndklansdlkan" ,
 *      post_id: asdkaklnsdnaklsndlkasd,
 *  }
 *
 *  1 { service } -> N [ 200 ]
 *
 *  ℹ️ℹ️ℹ️ℹ️ℹ️ℹ️ℹ️ℹ️ℹ️ℹ️ℹ️ℹ️ℹ️ℹ️ℹ️ℹ️ℹ️ℹ️ℹ️ℹ️ℹ️ℹ️ℹ️ℹ️ℹ️
 *
 *  create post [ ✅]
 *  create event [✅ ]
 *  create service [ ✅]
 *  follow ✅
 *  comment ✅ ✅
 *  reaction ✅
 *  bookmark-post ✅
 *
 *
 *
 *
 *
 *
 *
 *
 *  ℹ️ℹ️ℹ️ℹ️ℹ️ℹ️ℹ️ℹ️ℹ️ℹ️ℹ️ℹ️ℹ️ℹ️ℹ️ℹ️ℹ️ℹ️ℹ️ℹ️ℹ️ℹ️ℹ️ℹ️ℹ️
 *
 *
 *
 *
 */

export interface Notifications {
  id?: string;
  timestamp?: Timestamp;
  deleted?: boolean;

  src_id: string; //  <source user_id>
  dest_id: string; // <destination user_id>
  event: NotificationEvents;

  order_id?:string; // for order status change
  order_status?:string;

  comment?: string;
  comment_id?: string;

  post_id?: string; //  for bookmark , reaction
  post_url?: string;
  thumbnail_url?: string;

  // optional for getNotification only
  src_details?: any;
}

export interface UserNotificationSummary {
  unread_count: number;
  last_updated: Timestamp;
}

export interface BulkNotificationInput {
  userId: string;
  followers: { id: string }[];
  event: NotificationEvents;
  postData?: {
    postFile?: string;
    thumbnailUrl?: string;
    postId?: string;
  };
}

export class NotificationManager {
  readonly NOTIFICATION_COLLECTION = "notifications";
  readonly USER_NOTIFICATION_SUMMARY = "user_notification_summary";

  static instance: NotificationManager | null = null;

  private constructor() {}

  static getInstance() {
    if (!this.instance) {
      this.instance = new NotificationManager();
    }
    return this.instance;
  }

  removeUndefined(obj: Record<string, any>) {
    return Object.fromEntries(Object.entries(obj).filter(([_, v]) => v !== undefined));
  }

  async CreateNotification({
    payload,
    check_duplicate = true,
  }: {
    payload: Notifications;
    check_duplicate?: boolean;
  }): Promise<"success" | "duplicate"> {
    try {
      const idToken = await getIdToken();
      const response = await fetch(GATE_URL, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${idToken}`,
        },
        body: JSON.stringify({
          type: "CreateNotification",
          payload: {
            payload,
            check_duplicate,
          },
        }),
      });
      if (response.status !== 200) {
        const errorText = await response.text();
        console.log({ errorText });
      }
      const result = await response.json();
      console.log({ result });

      // const { db } = await initFirebase();

      // const notificationRef = collection(db, this.NOTIFICATION_COLLECTION);
      // console.log({ payload, notificationRef, check_duplicate });

      // // payload
      // // check for duplicate
      // if (check_duplicate) {
      //   // key [src_id , dest_id , event , comment , comment_id , post_id , post_url]
      //   const filters = [
      //     where("src_id", "==", payload.src_id),
      //     where("dest_id", "==", payload.dest_id),
      //     where("event", "==", payload.event),
      //   ];

      //   if (payload.comment !== undefined) {
      //     filters.push(where("comment", "==", payload.comment));
      //   }
      //   if (payload.comment_id !== undefined) {
      //     filters.push(where("comment_id", "==", payload.comment_id));
      //   }
      //   if (payload.post_id !== undefined) {
      //     filters.push(where("post_id", "==", payload.post_id));
      //   }
      //   if (payload.post_url !== undefined) {
      //     filters.push(where("post_url", "==", payload.post_url));
      //   }
      //   if (payload.thumbnail_url !== undefined) {
      //     filters.push(where("thumbnail_url", "==", payload.thumbnail_url));
      //   }

      //   const duplicateQuery = query(notificationRef, ...filters);

      //   const dupSnap = await getDocs(duplicateQuery);

      //   console.log({ dupSnap });

      //   if (!dupSnap.empty) {
      //     return "duplicate";
      //   }
      // }

      // const docRef = await addDoc(
      //   notificationRef,
      //   Object.fromEntries(
      //     Object.entries({
      //       ...payload,
      //       timestamp: serverTimestamp(),
      //       deleted: false,
      //     }).filter(([_, v]) => v !== undefined)
      //   )
      //   //    {
      //   //   ...payload,
      //   //   timestamp: serverTimestamp(),
      //   //   deleted: false,
      //   // }
      // );
      // await setDoc(
      //   docRef,
      //   Object.fromEntries(
      //     Object.entries({
      //       ...payload,
      //       id: docRef.id,
      //       timestamp: serverTimestamp(),
      //       deleted: false,
      //       read: false,
      //     }).filter(([_, v]) => v !== undefined)
      //   )
      //   //   {
      //   //   ...payload,
      //   //   id: docRef.id,
      //   //   timestamp: serverTimestamp(),
      //   //   deleted: false,
      //   //   read: false,
      //   // }
      // );

      // this.IncrementUnreadCount({ user_id: payload.dest_id });

      return "success";
    } catch (error) {
      console.log({ error });

      throw new Error("createNotificationFailed");
    }
  }

  async IncrementUnreadCount({ user_id }: { user_id: string }): Promise<void> {
    try {
      const { db } = await initFirebase();

      const summaryRef = doc(db, this.USER_NOTIFICATION_SUMMARY, user_id);
      await setDoc(
        summaryRef,
        {
          unread_count: increment(1),
          last_updated: serverTimestamp(),
        },
        { merge: true }
      );
    } catch (error) {
      console.log({ error });
    }
  }

  async ResetUserNotificationSummary({ user_id }: { user_id: string }): Promise<void> {
    try {
      const { db } = await initFirebase();
      const summaryRef = doc(db, this.USER_NOTIFICATION_SUMMARY, user_id);

      await updateDoc(summaryRef, {
        unread_count: 0,
        last_updated: serverTimestamp(),
      });
    } catch (error) {
      console.error("Failed to reset UserNotificationSummary:", error);
      throw new Error("ResetUserNotificationSummaryFailed");
    }
  }

  // Unread notification count
  async UserUnreadNotificationCount({ user_id }: { user_id: string }): Promise<number> {
    try {
      const idToken = await getIdToken();
      const response = await fetch(GATE_URL, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${idToken}`,
        },
        body: JSON.stringify({
          type: "UserUnreadNotificationCount",
          payload: {
            user_id,
          },
        }),
      });
      if (response.status !== 200) {
        const errorText = await response.text();
        console.log({ errorText });
      }
      const result = await response.json();
      console.log({ result });
      return result?.message ?? 0;
      // const { db } = await initFirebase();
      // const summaryRef = doc(db, "user_notification_summary", user_id);
      // const snapshot = await getDoc(summaryRef);

      // if (!snapshot.exists()) return 0;

      // const data = snapshot.data() as UserNotificationSummary;
      // return data.unread_count ?? 0;
    } catch (error) {
      console.error("Failed to fetch unread count:", error);
      throw new Error("GetUserUnreadNotificationCountFailed");
    }
  }

  async GetNotificationsByUserId({ user_id }: { user_id: string }): Promise<Notifications[]> {
    try {
      const idToken = await getIdToken();
      const response = await fetch(GATE_URL, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${idToken}`,
        },
        body: JSON.stringify({
          type: "GetNotificationsByUserId",
          payload: {
            user_id,
          },
        }),
      });
      if (response.status !== 200) {
        const errorText = await response.text();
        console.log({ errorText });
      }
      const result = await response.json();
      console.log({ result });
      return result?.message ?? [];
      // const { db } = await initFirebase();
      // const notificationsRef = collection(db, this.NOTIFICATION_COLLECTION);

      // const q = query(
      //   notificationsRef,
      //   where("dest_id", "==", user_id),
      //   orderBy("timestamp", "desc") // Sort newest to oldest
      // );

      // const snapshot = await getDocs(q);

      // const notifications: Notifications[] = snapshot.docs.map((doc) => ({
      //   id: doc.id,
      //   ...doc.data(),
      // })) as Notifications[];

      // // Step 1: Extract unique src_ids
      // const srcIds = [...new Set(notifications.map((n) => n.src_id))];

      // // Step 2: Fetch all corresponding user documents
      // const usersRef = collection(db, "users");
      // const srcUserDocs = await Promise.all(
      //   srcIds.map(async (id) => {
      //     const docRef = doc(usersRef, id);
      //     const userSnap = await getDoc(docRef);
      //     return userSnap.exists() ? { id, ...userSnap.data() } : null;
      //   })
      // );

      // // Step 3: Map user data by user ID
      // const userMap = new Map(srcUserDocs.filter(Boolean).map((user:any) => [user.id, user]));

      // // Step 4: Enrich notifications with src_details details
      // const enrichedNotifications = notifications.map((n) => ({
      //   ...n,
      //   src_details: userMap.get(n.src_id) || null,
      // }));

      // if ((await this.UserUnreadNotificationCount({ user_id })) > 0) {
      //   await this.ResetUserNotificationSummary({ user_id });
      // }
      // if ((await this.UserUnreadNotificationCount({ user_id })) > 0) {
      //   await this.ResetUserNotificationSummary({ user_id });
      // }
      // return enrichedNotifications;
    } catch (error) {
      console.log({ error });

      throw new Error("GetNotificationsByUserIdFailed");
    }
  }

  async BulkCreateNotificationsAndUpdateUnreadCounts({
    userId,
    followers,
    postData,
    event,
  }: BulkNotificationInput): Promise<void> {
    try {
      const idToken = await getIdToken();
      const response = await fetch(GATE_URL, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${idToken}`,
        },
        body: JSON.stringify({
          type: "BulkCreateNotificationsAndUpdateUnreadCounts",
          payload: {
            userId,
            followers,
            postData,
            event,
          },
        }),
      });
      if (response.status !== 200) {
        const errorText = await response.text();
        console.log({ errorText });
      }
      const result = await response.json();

      // const { db } = await initFirebase();
      // const BATCH_LIMIT = 500;

      // const affectedUserIds = new Set<string>();
      // let batch = writeBatch(db);
      // let opCount = 0;

      // const notificationCollection = collection(db, this.NOTIFICATION_COLLECTION);

      // // Create notifications in batches
      // for (let i = 0; i < followers.length; i++) {
      //   const followerId = followers[i].id;

      //   let notificationPayload: Notifications = {
      //     src_id: userId,
      //     dest_id: followerId,
      //     event,
      //   };

      //   if (postData) {
      //     notificationPayload = {
      //       ...notificationPayload,
      //       ...(postData?.postId && { post_id: postData.postId }),
      //       ...(postData?.postFile && { post_url: postData.postFile }),
      //       ...(postData?.thumbnailUrl && { thumbnail_url: postData.thumbnailUrl }),
      //     };
      //   }

      //   const docRef = doc(notificationCollection); // Firestore will auto-assign ID
      //   batch.set(docRef, {
      //     ...notificationPayload,
      //     id: docRef.id,
      //     timestamp: serverTimestamp(),
      //     deleted: false,
      //   });

      //   affectedUserIds.add(followerId);
      //   opCount++;

      //   if (opCount === BATCH_LIMIT) {
      //     await batch.commit();
      //     batch = writeBatch(db);
      //     opCount = 0;
      //   }
      // }

      // if (opCount > 0) {
      //   const resp = await batch.commit();
      //   console.log({ resp });
      // }

      // // Update unread count batching
      // batch = writeBatch(db);
      // opCount = 0;

      // for (const user_id of affectedUserIds) {
      //   const summaryRef = doc(db, this.USER_NOTIFICATION_SUMMARY, user_id);
      //   batch.set(
      //     summaryRef,
      //     {
      //       unread_count: increment(1),
      //       last_updated: serverTimestamp(),
      //     },
      //     { merge: true }
      //   );

      //   opCount++;

      //   if (opCount === BATCH_LIMIT) {
      //     await batch.commit();
      //     batch = writeBatch(db);
      //     opCount = 0;
      //   }
      // }

      // if (opCount > 0) {
      //   await batch.commit();
      // }
    } catch (error) {
      console.log("---->>>>", { error });
      throw new Error("BulkCreateNotificationsAndUpdateUnreadCounts_Failed");
    }
  }
}
