import { NextRequest, NextResponse } from 'next/server';

export async function GET(req: NextRequest) {
  try {
    const url = new URL(req.url);
    const userId = url.searchParams.get('userId');

    if (!userId) {
      return NextResponse.json(
        { error: 'userId parameter is required' },
        { status: 400 }
      );
    }

    console.log('Testing seller API for user:', userId);

    // Test the seller API endpoint
    const baseUrl = url.origin;
    const sellerResponse = await fetch(`${baseUrl}/api/sellers/${userId}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    const sellerData = await sellerResponse.json();

    return NextResponse.json({
      success: true,
      userId,
      sellerApiStatus: sellerResponse.status,
      sellerData,
      timestamp: new Date().toISOString(),
    });

  } catch (error) {
    console.error('Error testing seller API:', error);
    return NextResponse.json(
      { 
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}
