import { NextRequest, NextResponse } from 'next/server';
import { stripe } from '@/lib/stripe';

export async function POST(req: NextRequest) {
  try {
    const { accountId, components } = await req.json();

    if (!accountId) {
      return NextResponse.json(
        { error: 'Account ID is required' },
        { status: 400 }
      );
    }

    // Default components if none specified
    const enabledComponents = components || [
      'account_onboarding',
      'account_management',
      'notification_banner',
      'payments',
      'payouts',
      'balances'
    ];

    // Create Account Session for Connect embedded components
    const accountSession = await stripe.accountSessions.create({
      account: accountId,
      components: {
        // Enable account onboarding component
        ...(enabledComponents.includes('account_onboarding') && {
          account_onboarding: {
            enabled: true,
          }
        }),
        // Enable account management component
        ...(enabledComponents.includes('account_management') && {
          account_management: {
            enabled: true,
          }
        }),
        // Enable notification banner component
        ...(enabledComponents.includes('notification_banner') && {
          notification_banner: {
            enabled: true,
          }
        }),
        // Enable payments component
        ...(enabledComponents.includes('payments') && {
          payments: {
            enabled: true,
            features: {
              refund_management: true,
              dispute_management: true,
              capture_payments: true,
            }
          }
        }),
        // Enable payouts component
        ...(enabledComponents.includes('payouts') && {
          payouts: {
            enabled: true,
            features: {
              instant_payouts: true,
              standard_payouts: true,
              edit_payout_schedule: true,
            }
          }
        }),
        // Enable balances component
        ...(enabledComponents.includes('balances') && {
          balances: {
            enabled: true,
            features: {
              instant_payouts: true,
              standard_payouts: true,
              edit_payout_schedule: true,
            }
          }
        }),
        // Enable payment details component
        ...(enabledComponents.includes('payment_details') && {
          payment_details: {
            enabled: true,
            features: {
              refund_management: true,
              dispute_management: true,
            }
          }
        }),
        // Enable documents component
        ...(enabledComponents.includes('documents') && {
          documents: {
            enabled: true,
          }
        }),
      }
    });

    return NextResponse.json({
      success: true,
      clientSecret: accountSession.client_secret,
      accountId,
      components: enabledComponents
    });

  } catch (error) {
    console.error('Error creating account session:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    
    return NextResponse.json(
      { 
        success: false,
        error: errorMessage 
      },
      { status: 500 }
    );
  }
}
