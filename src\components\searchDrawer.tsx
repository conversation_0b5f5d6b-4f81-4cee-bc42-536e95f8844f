"use client";
import { useEffect, useState } from "react";
import { She<PERSON>, SheetContent } from "@/components/ui/sheet";
import { Modal, ModalContent, ModalBody } from "@heroui/react";
import { Filter, X } from "react-feather";
import { ChevronLeft } from "lucide-react";
import { usePathname } from "next/navigation";
import Profiles from "./search/Profiles";
import Posts from "./search/Posts";
import Services from "./search/Services";
import Events from "./search/Events";
import SidebarFilter from "./SidebarFilter";

const SEARCH_FOR = ["Profiles", "Posts", "Services", "Events"];
const SEARCH_BY_MAP: Record<string, string[]> = {
  Profiles: ["Profile name", "Hashtag", "Location"],
  Posts: ["Description", "Hashtag", "Location"],
  Services: ["Service name", "Description"],
  Events: ["Name", "Description"],
};

function useIsMobile(breakpoint = 768) {
  const [isMobile, setIsMobile] = useState(false);
  useEffect(() => {
    const check = () => setIsMobile(window.innerWidth < breakpoint);
    check();
    window.addEventListener("resize", check);
    return () => window.removeEventListener("resize", check);
  }, [breakpoint]);
  return isMobile;
}

export default function SearchDrawer({
  open,
  onOpenChange,
}: {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}) {
  const [searchFor, setSearchFor] = useState("Profiles");
  const [searchBy, setSearchBy] = useState("");
  const [search, setSearch] = useState("");
  const [showFilter, setShowFilter] = useState(false);
  const [filters, setFilters] = useState<{
    category: string[];
    profileNames: string[];
    location: string[];
    date_of_publishing: string; // changed from string[] to string
  }>({
    category: [],
    profileNames: [],
    location: [],
    date_of_publishing: "", // default to empty string
  });
  const pathname = usePathname();
  const isMobile = useIsMobile();

  // Update searchBy options when searchFor changes
  useEffect(() => {
    if (searchFor) {
      setSearchBy(SEARCH_BY_MAP[searchFor][0]);
    } else {
      setSearchBy("");
    }
  }, [searchFor]);

  // Dynamically filter SEARCH_FOR based on route
  const searchForOptions =
    pathname && pathname.includes("/browse") ? ["Profiles", "Posts"] : SEARCH_FOR;

  const searchByOptions = searchFor ? SEARCH_BY_MAP[searchFor] : [];
  // Check if filters has at least one valid value
  const hasData = Object.values(filters || {}).some((val) => {
    if (Array.isArray(val)) return val.length > 0; // check arrays
    return val !== undefined && val !== null && val !== ""; // check strings/values
  });

  useEffect(() => {
    setFilters({
      category: [],
      profileNames: [],
      location: [],
      date_of_publishing: "",
    });
    setSearch("");
  }, [searchFor]);

  const handleApplyFilters = (filters: {
    category: string[];
    profileNames: string[];
    location: string[];
    date_of_publishing: string;
  }) => {
    // Map UI filter keys to backend filter keys
    const mappedFilters = {
      category: filters.category || [],
      location: filters.location || [],
      user_id: [], // You can map profileNames to user_id if you have the mapping
      date_of_publishing: filters.date_of_publishing || undefined, // now a string, not array
    };
    setFilters(filters);
    // setShowFilter(false);
  };

  const DrawerContent = (
    <div className="bg-white h-full flex flex-col">
      {/* Header row */}
      <div className="flex items-center justify-between px-6 pt-6 pb-2">
        <button
          className="flex items-center text-black text-base font-medium"
          onClick={() => onOpenChange(false)}
        >
          <ChevronLeft className="w-5 h-5 mr-1" />
          Back
        </button>
        <div className="flex-1 flex justify-center">
          <div className="w-full max-w-xl">
            <div className="relative">
              <input
                type="text"
                placeholder="Search"
                className="w-full border border-gray-200 rounded-md pl-4 pr-10 py-2 bg-[#FAFAFA] text-gray-900 focus:outline-none focus:ring-2 focus:ring-primary"
                value={search}
                onChange={(e) => setSearch(e.target.value)}
              />
              <span className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-400">
                <svg
                  width="20"
                  height="20"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  viewBox="0 0 24 24"
                >
                  <circle cx="11" cy="11" r="8" />
                  <line x1="21" y1="21" x2="16.65" y2="16.65" />
                </svg>
              </span>
            </div>
          </div>
        </div>
        {!showFilter && (
          <button
            className="text-black text-base font-medium"
            onClick={() => {
              if (searchFor) {
                setShowFilter(true);
              } else {
                onOpenChange(false);
              }
            }}
          >
            <Filter
              size={34}
              color="#333333"
              fill={hasData ? "#333333" : "none"}
              // onClick={() => setSheetOpenFilter(true)}
              className=" cursor-pointer max-md:w-[24px] max-md:h-[24px]"
              strokeWidth={1.5}
            />
            {/* {searchFor ? "Filter" : "Cancel"} */}
          </button>
        )}
      </div>
      {/* Button groups (scrollable section) */}
      <div className="flex flex-col gap-3 px-8 pt-4 overflow-y-auto max-h-56">
        <div className="flex items-center gap-3">
          <div className="flex items-center gap-3">
            <span className="text-gray-400 text-base mr-2 text-nowrap">Search for</span>
            {/* {searchFor ? (
              <button
                className={`px-4 py-1.5 text-nowrap rounded-md text-base font-medium transition-colors bg-[#333] text-white`}
                disabled
              >
                {searchFor}
              </button>
            ) : ( */}
            {searchForOptions.map((item) => (
              <button
                key={item}
                onClick={() => setSearchFor(item)}
                className={
                  searchFor === item
                    ? "px-4 py-1.5 text-nowrap rounded-md text-base font-medium transition-colors bg-[#333] text-white hover:bg-[#E5E5E5]"
                    : `px-4 py-1.5 text-nowrap rounded-md text-base font-medium transition-colors bg-[#F5F5F5] text-gray-700 hover:bg-[#E5E5E5]`
                }
              >
                {item}
              </button>
            ))}

            {/* )) */}
            {/* )} */}
          </div>
          {searchFor && (
            <button
              className="ml-4 text-gray-500 text-sm underline hover:text-black"
              onClick={() => setSearchFor("Profiles")}
            >
              Clear
            </button>
          )}
        </div>
        {/* {searchFor && ( */}
        <div className="flex items-center gap-3">
          <span className="text-gray-400 text-base mr-2 text-nowrap">Search by</span>
          {searchByOptions.map((item) => (
            <button
              key={item}
              onClick={() => setSearchBy(item)}
              className={`px-4 py-1.5 rounded-md text-base font-medium transition-colors text-nowrap ${searchBy === item ? "bg-[#333] text-white" : "bg-[#F5F5F5] text-gray-700"}`}
            >
              {item}
            </button>
          ))}
        </div>
        {/* )} */}
      </div>
      {/* Search content */}
      <div className="flex-1 min-h-0 px-8 pt-4 pb-6 overflow-y-auto">
        {searchFor === "Profiles" && (
          <Profiles
            searchFor={searchFor}
            searchBy={searchBy}
            search={search}
            SEARCH_FOR={searchForOptions}
            SEARCH_BY_MAP={SEARCH_BY_MAP}
            onClose={() => onOpenChange(false)}
            filters={filters}
          />
        )}
        {searchFor === "Posts" && (
          <Posts
            searchFor={searchFor}
            searchBy={searchBy}
            search={search}
            SEARCH_FOR={searchForOptions}
            SEARCH_BY_MAP={SEARCH_BY_MAP}
            onClose={() => onOpenChange(false)}
            filters={filters}
          />
        )}
        {searchFor === "Services" && (
          <Services
            searchFor={searchFor}
            searchBy={searchBy}
            search={search}
            SEARCH_FOR={searchForOptions}
            SEARCH_BY_MAP={SEARCH_BY_MAP}
            onClose={() => onOpenChange(false)}
            filters={filters}
          />
        )}
        {searchFor === "Events" && (
          <Events
            searchFor={searchFor}
            searchBy={searchBy}
            search={search}
            SEARCH_FOR={searchForOptions}
            SEARCH_BY_MAP={SEARCH_BY_MAP}
            onClose={() => onOpenChange(false)}
            filters={filters}
          />
        )}
      </div>
    </div>
  );

  return (
    <>
      <Modal
        isDismissable={false}
        isKeyboardDismissDisabled={true}
        isOpen={open}
        onOpenChange={onOpenChange}
        size="full"
        placement="auto"
        hideCloseButton={true}
        className="z-[99999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999]"
      >
        <ModalContent>
          {(onClose) => (
            <ModalBody className="p-0">
              <div className="flex h-full">
                <div className={showFilter ? "flex-1" : "w-full"}>{DrawerContent}</div>
                {showFilter &&
                  (isMobile ? (
                    <div className="fixed inset-0 z-50 bg-white">
                      <SidebarFilter
                        onOpenChange={() => setShowFilter(false)}
                        onApply={handleApplyFilters}
                        filters={filters}
                      />
                    </div>
                  ) : (
                    <div className="w-[22rem] border-l h-full bg-white">
                      <SidebarFilter
                        onOpenChange={() => setShowFilter(false)}
                        onApply={handleApplyFilters}
                        filters={filters}
                      />
                    </div>
                  ))}
              </div>
            </ModalBody>
          )}
        </ModalContent>
      </Modal>
    </>
  );
}
