"use client";
import React, { useEffect, useRef, useState } from "react";
import { themes } from "../../../../theme";
import useAuth from "@/hook";
import CategoryComp from "@/globalComponents/homeComp/categoryComp";
import { MainContentFocus, PageSize, PostType, usePostsQuery } from "@/graphql/test/generated";
import NewPostMobileView from "./newPostMobileView";
import Web3PostMobileView from "./web3PostMobileView";
import { useFilter } from "@/context/FilterContext";

const data = [
  {
    img: "/assets/img.svg",
    imgMobile: "/assets/mobile-Img/myfeed.png",
    title: "My Feed",
    backgroundColor: "#000000",
  },
  {
    img: "/assets/music.svg",
    imgMobile: "/assets/mobile-Img/music.png",
    title: "Music",
    backgroundColor: "#E5B045",
  },
  {
    img: "/assets/litrature.svg",
    imgMobile: "/assets/mobile-Img/lit.png",
    title: "Literature",
    backgroundColor: "#CF5943",
  },
  {
    img: "/assets/art.svg",
    imgMobile: "/assets/mobile-Img/art.png",
    backgroundColor: "#3C5F9A",
    title: "Art",
  },
  {
    img: "/assets/film.svg",
    imgMobile: "/assets/mobile-Img/film.png",
    title: "Film & Photography",
    backgroundColor: "#46B933",
  },
  {
    img: "/assets/Theatre.svg",
    imgMobile: "/assets/mobile-Img/theater.png",
    title: "Theatre & Performance",
    backgroundColor: "#E073D2",
  },
  {
    img: "/assets/multi.svg",
    imgMobile: "/assets/mobile-Img/multi.png",
    title: "Multidisciplinary",
    backgroundColor: "#5331BC",
  },
  {
    img: "/assets/groups.svg",
    imgMobile: "/assets/mobile-Img/group.png",
    title: "Groups",
    backgroundColor: "#616770",
  },
];

const MobilePostsHome = () => {
  const scrollRef = useRef<HTMLDivElement>(null);
  const user = useAuth();
  const { filters } = useFilter();

  // Enhanced Swipe Function
  const [touchStart, setTouchStart] = useState<{
    x: number;
    y: number;
    time: number;
    index: number;
  } | null>(null);
  const [touchEnd, setTouchEnd] = useState<{ x: number; y: number; time: number } | null>(null);
  const [isSwiping, setIsSwiping] = useState(false);

  // We no longer need to track swipe offsets as we're using CSS transitions instead

  // Track if each category's scroll is at the left edge
  const [isAtLeftEdge, setIsAtLeftEdge] = useState<boolean[]>(new Array(data.length).fill(true));

  // Create refs for each category's scroll container
  const scrollContainerRefs = useRef<(HTMLDivElement | null)[]>(new Array(data.length).fill(null));

  // Track swipe state for each section
  const [swipedSections, setSwipedSections] = useState<boolean[]>(
    new Array(data.length).fill(false)
  );

  // Configuration for swipe detection
  const minSwipeDistance = 40; // Reduced for more sensitivity
  const minSwipeVelocity = 0.3; // Minimum velocity to trigger swipe (pixels per ms)
  const maxSwipeTime = 300; // Maximum time for a swipe gesture (ms)

  // Handle touch start
  const onTouchStart = (e: React.TouchEvent, index: number) => {
    const touch = e.targetTouches[0];
    setTouchStart({
      x: touch.clientX,
      y: touch.clientY,
      time: Date.now(),
      index: index, // Store which category is being swiped
    });
    setTouchEnd(null);
    setIsSwiping(true);
  };

  // Handle touch move
  const onTouchMove = (e: React.TouchEvent) => {
    if (!touchStart) return;

    const touch = e.targetTouches[0];
    const currentTouch = {
      x: touch.clientX,
      y: touch.clientY,
      time: Date.now(),
    };

    setTouchEnd(currentTouch);

    // Calculate horizontal offset for visual feedback during swipe
    const offset = currentTouch.x - touchStart.x;

    // We're not updating swipe offsets during movement anymore
    // This prevents the categories from moving during the swipe

    // Prevent default to avoid scrolling while swiping horizontally
    // Only if the swipe is primarily horizontal
    const verticalOffset = Math.abs(currentTouch.y - touchStart.y);
    if (Math.abs(offset) > 10 && Math.abs(offset) > verticalOffset) {
      // Using passive event listeners, so we can't call preventDefault
      // Instead, we'll handle this with CSS to prevent scrolling
    }
  };

  // Handle touch end
  const onTouchEnd = (index: number) => {
    if (!touchStart || !touchEnd) {
      setIsSwiping(false);
      return;
    }

    const distance = touchStart.x - touchEnd.x;
    const time = touchEnd.time - touchStart.time;
    const velocity = Math.abs(distance) / time; // pixels per ms

    const isLeftSwipe = distance > minSwipeDistance && velocity > minSwipeVelocity;
    const isRightSwipe = distance < -minSwipeDistance && velocity > minSwipeVelocity;

    // Check if the swipe was primarily horizontal
    const verticalDistance = Math.abs(touchEnd.y - touchStart.y);
    const isHorizontalSwipe = Math.abs(distance) > verticalDistance;

    // Reset swipe state
    setIsSwiping(false);

    // Only process horizontal swipes that meet our criteria
    if (isHorizontalSwipe && time < maxSwipeTime) {
      // Check if we're in the main view or the swiped view
      if (!swipedSections[index]) {
        // We're in the main view - only allow left swipe if at left edge
        if (isLeftSwipe) {
          // Only switch view if we're at the left edge
          if (isAtLeftEdge[index]) {
            // At left edge, so switch view
            setSwipedSections((prev) => prev.map((item, idx) => (idx === index ? true : item)));
          }
          // If not at left edge, the horizontal scroll will handle the swipe naturally
        }
      } else {
        // We're in the swiped view - always allow right swipe to go back
        if (isRightSwipe) {
          // For all categories, including the first one (Music for logged out, My Feed for logged in)
          setSwipedSections((prev) => prev.map((item, idx) => (idx === index ? false : item)));

          // Debug log to verify the swipe is detected
          console.log(`Right swipe detected for category index ${index}`);
        }
      }
    }
  };

  // Handle scroll events for better mobile experience
  useEffect(() => {
    const handleScroll = () => {
      // We're not using dynamic height anymore, but keeping the scroll handler
      // for potential future enhancements
    };

    window.addEventListener("scroll", handleScroll);

    return () => {
      window.removeEventListener("scroll", handleScroll);
    };
  }, []);

  function hexToRGBA(hex: string, alpha: number): string {
    const rgb = hex
      .replace("#", "")
      .match(/.{1,2}/g)
      ?.map((x) => parseInt(x, 16));
    return rgb ? `rgba(${rgb[0]}, ${rgb[1]}, ${rgb[2]}, ${alpha})` : hex;
  }

  // Simplified state for Lens data
  const [profileData] = useState<any>(null);

  const {
    isLoading: isLoadingPublications,
    data: publicationsQueryData,
    error: publicationsQueryError,
  } = usePostsQuery(
    {
      request: {
        filter: {
          authors: profileData?.profile?.id,
          postTypes: [PostType.Root],
          metadata: {
            mainContentFocus: [MainContentFocus.Image, MainContentFocus.Video],
          },
        },
        pageSize: PageSize.Fifty,
      },
    },
    {
      refetchOnWindowFocus: false,
      enabled: !!profileData?.profile?.id, // Only execute query when profile ID exists
    }
  );

  // Monitor publications data loading
  useEffect(() => {
    // We're not updating state anymore, but keeping this effect
    // to monitor data loading for potential future enhancements
    if (publicationsQueryData) {
      // Data loaded successfully
    }
    if (publicationsQueryError) {
      // Handle error if needed
      console.error("Error loading publications:", publicationsQueryError);
    }
  }, [isLoadingPublications, publicationsQueryData, publicationsQueryError]);

  return (
    <div className="relative p-0 h-full">
      <div ref={scrollRef} className="overflow-y-auto p-0 hide-scroll h-full">
        <div className={`space-y-0 flex flex-col w-full gap-4 pb-20 ${user.isLogin ? "pl-0" : ""}`}>
          {(() => {
            const filteredData = data.filter((item) => {
              // Apply category filtering
              if (filters.categories && filters.categories.length > 0) {
                return filters.categories.includes(item.title);
              }
              return true; // Show all categories if no filter is applied
            });

            // Show message when no categories match the filter
            if (filters.categories && filters.categories.length > 0 && filteredData.length === 0) {
              return (
                <div className="w-full flex items-center justify-center min-h-[400px]">
                  <div className="text-center p-8 bg-gray-50 rounded-lg border border-gray-200 max-w-md">
                    <div className="mb-4">
                      <svg
                        className="w-16 h-16 text-gray-400 mx-auto"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth="2"
                          d="M9.172 16.172a4 4 0 015.656 0M9 12h6m-6-4h6m2 5.291A7.962 7.962 0 0112 15c-2.34 0-4.467-.881-6.08-2.33"
                        />
                      </svg>
                    </div>
                    <h3 className="text-lg font-medium text-gray-900 mb-2">
                      No matching categories
                    </h3>
                    <p className="text-gray-500">
                      No posts found for the selected categories. Try adjusting your filter
                      settings.
                    </p>
                  </div>
                </div>
              );
            }

            return filteredData.map((item, index) => {
              return (
                <div
                  key={`${item.title}-${filters.categories?.join(",") || "all"}`}
                  ref={scrollRef}
                >
                  {!swipedSections[index] ? (
                    <div
                      className="transition-all duration-300 ease-in-out"
                      key={index}
                      onTouchStart={(e) => onTouchStart(e, index)}
                      onTouchMove={(e) => onTouchMove(e)}
                      onTouchEnd={() => onTouchEnd(index)}
                    >
                      {!user.isLogin && !(item.title == "My Feed") && (
                        <>
                          <CategoryComp item={item} />
                        </>
                      )}

                      {user.isLogin && (
                        <>
                          <CategoryComp item={item} />
                        </>
                      )}

                      <div className="md:overflow-scroll hide-scroll md:h-full">
                        {/* for mobile view */}
                        <div
                          className="row overflow-x-clip w-full pt-0 -mt-[2px]"
                          ref={(el) => {
                            scrollContainerRefs.current[index] = el;
                          }}
                          onScroll={(e) => {
                            const target = e.currentTarget;
                            const atLeftEdge = target.scrollLeft <= 1;
                            setIsAtLeftEdge((prev) => {
                              const newEdges = [...prev];
                              newEdges[index] = atLeftEdge;
                              return newEdges;
                            });
                          }}
                          style={{
                            backgroundColor: item.backgroundColor.startsWith("#")
                              ? hexToRGBA(item.backgroundColor, 0.7)
                              : item.backgroundColor,
                          }}
                        >
                          <div className="">
                            {Object.entries(themes).map(
                              ([_, themeProperties]) =>
                                themeProperties.title === item.title && (
                                  <div key={themeProperties.title} className="">
                                    {!user.isLogin && !(item.title == "My Feed") && (
                                      <div className="">
                                        {/* <PostMobileView
                                        themeProperties={themeProperties}
                                        borderColor={themeProperties.backgroundColor}
                                      /> */}
                                        <NewPostMobileView
                                          key={`new-mobile-${themeProperties.title}-${filters.categories?.join(",") || "all"}`}
                                          themeProperties={themeProperties}
                                          borderColor={themeProperties.backgroundColor}
                                          category={themeProperties.title}
                                          user_id={user?.userId}
                                        />
                                      </div>
                                    )}
                                    {user.isLogin && (
                                      <div className="w-full py-0 pl-0">
                                        <div className="">
                                          {/* <PostMobileView
                                          themeProperties={themeProperties}
                                          borderColor={themeProperties.backgroundColor}
                                        /> */}
                                          <NewPostMobileView
                                            key={`new-mobile-logged-${themeProperties.title}-${filters.categories?.join(",") || "all"}`}
                                            themeProperties={themeProperties}
                                            borderColor={themeProperties.backgroundColor}
                                            category={themeProperties.title}
                                            user_id={user?.userId}
                                          />
                                        </div>
                                      </div>
                                    )}
                                  </div>
                                )
                            )}
                          </div>
                        </div>
                      </div>
                    </div>
                  ) : (
                    <div
                      className="transition-all duration-300 ease-in-out"
                      onTouchStart={(e) => onTouchStart(e, index)}
                      onTouchMove={(e) => onTouchMove(e)}
                      onTouchEnd={() => onTouchEnd(index)}
                    >
                      {/* For non-logged in users */}
                      {!user.isLogin && (
                        <div key={index} className="transition-all duration-300 ease-in-out">
                          {/* Skip rendering content for My Feed when not logged in */}
                          {item.title !== "My Feed" && (
                            <>
                              <div
                                className="w-full rounded-tl-lg -mb-[1px]"
                                style={{
                                  backgroundColor: item.backgroundColor.startsWith("#")
                                    ? hexToRGBA(item.backgroundColor, 0.7)
                                    : item.backgroundColor,
                                }}
                              >
                                <p className="pl-4 text-md font-semibold text-white ">
                                  {item.title}
                                </p>
                              </div>

                              <div
                                className="row overflow-x-scroll w-full"
                                ref={(el) => {
                                  scrollContainerRefs.current[index] = el;
                                }}
                                onScroll={(e) => {
                                  const target = e.currentTarget;
                                  const atLeftEdge = target.scrollLeft <= 1;
                                  setIsAtLeftEdge((prev) => {
                                    const newEdges = [...prev];
                                    newEdges[index] = atLeftEdge;
                                    return newEdges;
                                  });
                                }}
                                style={{
                                  backgroundColor: item.backgroundColor.startsWith("#")
                                    ? hexToRGBA(item.backgroundColor, 0.7)
                                    : item.backgroundColor,
                                }}
                              >
                                {Array.from({ length: 1 }).map((_, indexs) => (
                                  <div key={indexs} className="row gap-0 mr-[0px]">
                                    {Object.entries(themes).map(
                                      ([_, themeProperties]) =>
                                        themeProperties.title === item.title && (
                                          <div className="w-full" key={indexs}>
                                            <NewPostMobileView
                                              key={`new-mobile-scroll-${themeProperties.title}-${filters.categories?.join(",") || "all"}`}
                                              themeProperties={themeProperties}
                                              borderColor={themeProperties.backgroundColor}
                                              category={themeProperties.title}
                                              isScroll={true}
                                              user_id={user?.userId}
                                            />
                                            <Web3PostMobileView
                                              key={`web3-mobile-scroll-${themeProperties.title}-${filters.categories?.join(",") || "all"}`}
                                              themeProperties={themeProperties}
                                              borderColor={themeProperties.backgroundColor}
                                              category={themeProperties.title}
                                              isScroll={true}
                                            />
                                          </div>
                                        )
                                    )}
                                  </div>
                                ))}
                              </div>
                            </>
                          )}
                        </div>
                      )}
                      {/* For logged in users */}
                      {user.isLogin && (
                        <div key={index} className="transition-all duration-300 ease-in-out">
                          <div
                            className="w-full rounded-tl-lg -mb-[1px]"
                            style={{
                              backgroundColor: item.backgroundColor.startsWith("#")
                                ? hexToRGBA(item.backgroundColor, 0.7)
                                : item.backgroundColor,
                            }}
                          >
                            <p className="pl-4 text-md font-semibold text-white ">{item.title}</p>
                          </div>

                          <div
                            className="row overflow-x-scroll"
                            ref={(el) => {
                              scrollContainerRefs.current[index] = el;
                            }}
                            onScroll={(e) => {
                              const target = e.currentTarget;
                              const atLeftEdge = target.scrollLeft <= 1;
                              setIsAtLeftEdge((prev) => {
                                const newEdges = [...prev];
                                newEdges[index] = atLeftEdge;
                                return newEdges;
                              });
                            }}
                            style={{
                              backgroundColor: item.backgroundColor.startsWith("#")
                                ? hexToRGBA(item.backgroundColor, 0.7)
                                : item.backgroundColor,
                            }}
                          >
                            {Array.from({ length: 1 }).map((_, indexs) => (
                              <div key={indexs} className="row gap-2">
                                {Object.entries(themes).map(
                                  ([_, themeProperties]) =>
                                    themeProperties.title === item.title && (
                                      <div className="w-full" key={indexs}>
                                        <div className="">
                                          {/* <PostMobileView
                                          themeProperties={themeProperties}
                                          isScroll={true}
                                          borderColor={themeProperties.backgroundColor}
                                        /> */}
                                          <NewPostMobileView
                                            key={`new-mobile-scroll2-${themeProperties.title}-${filters.categories?.join(",") || "all"}`}
                                            themeProperties={themeProperties}
                                            borderColor={themeProperties.backgroundColor}
                                            category={themeProperties.title}
                                            isScroll={true}
                                            user_id={user?.userId}
                                          />
                                          <Web3PostMobileView
                                            key={`web3-mobile-scroll2-${themeProperties.title}-${filters.categories?.join(",") || "all"}`}
                                            themeProperties={themeProperties}
                                            borderColor={themeProperties.backgroundColor}
                                            category={themeProperties.title}
                                            isScroll={true}
                                          />
                                        </div>
                                      </div>
                                    )
                                )}
                              </div>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                  )}
                </div>
              );
            });
          })()}
        </div>
      </div>
    </div>
  );
};

export default MobilePostsHome;
