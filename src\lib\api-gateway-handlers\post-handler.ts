import { Notifications, UserNotificationSummary } from "@/services/notificationService";
import { Post } from "@/services/postService";
import { getFirestore, FieldValue, Timestamp, WriteBatch } from "firebase-admin/firestore";

export class PostsHandlerManager {
  private POSTS_COLLECTION = "posts";

  static instance: PostsHandlerManager | null = null;

  private constructor() {}

  static getInstance() {
    if (!this.instance) {
      this.instance = new PostsHandlerManager();
    }
    return this.instance;
  }

  GetPostDetailsByPostId = async ({ post_id }: { post_id: string }): Promise<Post | null> => {
    try {
      const db = getFirestore();

      const postSnap = await db.collection(this.POSTS_COLLECTION).doc(post_id).get();
      console.log({postSnap});
      

      if (!postSnap.exists) {
        console.log(`Post with ID ${post_id} does not exist.`);
        return null;
      }

      return postSnap.data() as Post;
    } catch (error) {
      console.error("GetPostDetailsByPostId failed:", error);
      throw new Error("GetPostDetailsByPostId_failed");
    }
  };
}
