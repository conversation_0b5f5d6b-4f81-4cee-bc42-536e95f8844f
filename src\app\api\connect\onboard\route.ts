import { NextRequest, NextResponse } from 'next/server';
import { Stripe } from 'stripe';
import { getUserIdFromRequest } from '@/lib/auth/serverAuth';
import { doc, setDoc, getDoc } from 'firebase/firestore';
import { initFirebase } from '../../../../../firebaseConfig';
import { UpdateUserAccountId } from '@/services/ordersServices';

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY as string);

export async function POST(req: NextRequest) {
  try {
    // Parse request body first to get user data
    let body: any = {};
    let userId: string | undefined;
    let email: string | undefined;

    try {
      body = await req.json();
      userId = body.userId;
      email = body.email;
    } catch (error) {
      console.error('Error parsing request body:', error);
    }

    // If no user ID in body, try to get from auth
    if (!userId) {
      const authUserId = await getUserIdFromRequest(req);
      if (authUserId) {
        userId = authUserId;
      }
    }

    if (!userId) {
      return NextResponse.json(
        {
          error: 'Authentication required. Please log in to create a Stripe account.',
          code: 'AUTH_REQUIRED'
        },
        { status: 401 }
      );
    }

    console.log('Creating Stripe account for user:', userId, 'with email:', email);

    // Check if user already has a Stripe account
    try {
      const { db } = await initFirebase();
      const userDocRef = doc(db, 'users', userId);
      const userDoc = await getDoc(userDocRef);

      if (userDoc.exists()) {
        const userData = userDoc.data();
        if (userData.stripe_id) {
          console.log('User already has Stripe account:', userData.stripe_id);

          // Verify the account still exists in Stripe
          try {
            const existingAccount = await stripe.accounts.retrieve(userData.stripe_id);

            // Return existing account info
            return NextResponse.json({
              accountId: existingAccount.id,
              userId,
              existing: true,
              message: 'User already has a Stripe account'
            });
          } catch (stripeError) {
            console.log('Existing Stripe account not found, creating new one');
            // Continue to create new account if the old one doesn't exist
          }
        }
      }
    } catch (error) {
      console.log('Error checking existing account, proceeding with creation:', error);
    }

    // 1. Create a new Express account for the seller
    const accountData: any = { type: 'express' };

    // Add email to account creation if provided
    if (email) {
      accountData.email = email;
    }

    const account = await stripe.accounts.create(accountData);

    // 2. Save account to stripeAccounts collection and update user
    try {
      const { db } = await initFirebase();

      // Save to stripeAccounts collection
      const stripeAccountRef = doc(db, 'stripeAccounts', account.id);
      const stripeAccountData: any = {
        stripeAccountId: account.id,
        userId: userId,
        createdAt: new Date().toISOString(),
        onboardingComplete: false,
        accountType: 'express'
      };

      // Add email to stripeAccounts if provided
      if (email) {
        stripeAccountData.email = email;
      }

      await setDoc(stripeAccountRef, stripeAccountData, { merge: true });

      // Update user collection with stripe_id
      try {
        // First, try to update directly using Firestore
        // This is a fallback in case the user is not authenticated
        const userRef = doc(db, 'users', userId);
        await setDoc(userRef, {
          stripe_id: account.id,
          updated_at: new Date().toISOString()
        }, { merge: true });

        console.log('Successfully updated user with stripe_id via direct Firestore update');

        // Also try the service method as a backup
        try {
          const updateResult = await UpdateUserAccountId({
            user_id: userId,
            stripe_id: account.id
          });

          if (updateResult.success) {
            console.log('Successfully updated user with stripe_id via service');
          }
        } catch (serviceError) {
          console.log('Service update failed, but direct update succeeded:', serviceError);
          // This is expected if the user is not authenticated, so we don't treat it as an error
        }
      } catch (userUpdateError) {
        console.error('Error updating user with stripe_id:', userUpdateError);
        // Continue even if user update fails
      }

      console.log('Saved Stripe account to Firebase:', { userId, accountId: account.id, email });
    } catch (firebaseError) {
      console.error('Error saving to Firebase:', firebaseError);
      // Continue with onboarding even if Firebase save fails
    }

    // 3. Create an onboarding link
    const origin = req.headers.get('origin') || 'http://localhost:3000';
    const accountLink = await stripe.accountLinks.create({
      account: account.id,
      refresh_url: `${origin}/payment?userId=${userId}&refresh=true`,
      return_url: `${origin}/payment?account=${account.id}&userId=${userId}&onboarding=complete`,
      type: 'account_onboarding',
    });

    return NextResponse.json({
      url: accountLink.url,
      accountId: account.id,
      userId
    });
  } catch (error) {
    console.error('Error creating Stripe account:', error);
    return NextResponse.json(
      { error: 'Failed to create account' },
      { status: 500 }
    );
  }
}
