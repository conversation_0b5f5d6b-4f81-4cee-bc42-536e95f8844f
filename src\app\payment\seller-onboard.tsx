"use client";

import React, { useState, useEffect } from 'react';
import { useCurrentUser } from '@/hooks/useCurrentUser';
import { ConnectEmbeddedProvider } from '@/components/ConnectEmbeddedProvider';
import ConnectAccountOnboardingWrapper from '@/components/ConnectAccountOnboarding';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Loader2, AlertCircle, CheckCircle, User } from 'lucide-react';

interface SellerAccount {
  stripeAccountId: string;
  onboardingComplete: boolean;
  chargesEnabled: boolean;
  payoutsEnabled: boolean;
  email?: string;
  businessName?: string;
}

export default function SellerOnboard() {
  const [loading, setLoading] = useState(false);
  const [email, setEmail] = useState('');
  const [sellerAccount, setSellerAccount] = useState<SellerAccount | null>(null);
  const [error, setError] = useState<string | null>(null);
  const { user, loading: userLoading, isAuthenticated } = useCurrentUser();

  // Set default email when user loads
  useEffect(() => {
    if (user?.email) {
      setEmail(user.email);
    }
  }, [user?.email]);

  // Check if user already has a seller account
  useEffect(() => {
    const checkSellerAccount = async () => {
      if (!user?.uid) return;

      try {
        const response = await fetch(`/api/sellers/${user.uid}`);
        if (response.ok) {
          const data = await response.json();
          setSellerAccount(data);
        }
      } catch (err) {
        console.error('Error checking seller account:', err);
      }
    };

    checkSellerAccount();
  }, [user?.uid]);

  const handleCreateAccount = async () => {
    if (!user?.uid) {
      setError('Please log in first to become a seller');
      return;
    }

    if (!email.trim()) {
      setError('Please provide an email address');
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const res = await fetch('/api/connect/onboard', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userId: user.uid,
          email: email.trim(),
          businessName: user.displayName || user.email,
        })
      });

      const data = await res.json();

      if (data.error) {
        setError(data.error);
        return;
      }

      // Refresh seller account data
      const accountResponse = await fetch(`/api/sellers/${user.uid}`);
      if (accountResponse.ok) {
        const accountData = await accountResponse.json();
        setSellerAccount(accountData);
      }

    } catch (error) {
      console.error('Error creating account:', error);
      setError('Failed to create seller account');
    } finally {
      setLoading(false);
    }
  };

  const handleOnboardingComplete = (accountId: string) => {
    // Refresh account data after onboarding
    setSellerAccount(prev => prev ? {
      ...prev,
      onboardingComplete: true,
      chargesEnabled: true,
    } : null);
  };

  if (userLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <Card className="w-full max-w-md">
          <CardContent className="flex items-center justify-center p-8">
            <div className="text-center">
              <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
              <p className="text-gray-600">Loading...</p>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (!isAuthenticated) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <CardTitle className="flex items-center justify-center">
              <User className="h-6 w-6 mr-2" />
              Become a Seller
            </CardTitle>
            <CardDescription>
              Please log in to start the seller onboarding process.
            </CardDescription>
          </CardHeader>
        </Card>
      </div>
    );
  }

  // If user has an account but onboarding is not complete, show embedded onboarding
  if (sellerAccount && !sellerAccount.onboardingComplete) {
    return (
      <div className="min-h-screen bg-gray-50">
        <div className="container mx-auto px-4 py-8">
          <ConnectEmbeddedProvider
            accountId={sellerAccount.stripeAccountId}
            components={['account_onboarding']}
          >
            <ConnectAccountOnboardingWrapper
              onOnboardingComplete={handleOnboardingComplete}
              className="max-w-4xl mx-auto"
            />
          </ConnectEmbeddedProvider>
        </div>
      </div>
    );
  }

  // If user has completed onboarding, show success
  if (sellerAccount && sellerAccount.onboardingComplete) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <CardTitle className="flex items-center justify-center text-green-600">
              <CheckCircle className="h-6 w-6 mr-2" />
              Seller Account Active
            </CardTitle>
            <CardDescription>
              Your seller account is set up and ready to accept payments!
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="bg-green-50 p-4 rounded-lg">
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span>Charges:</span>
                  <span className={sellerAccount.chargesEnabled ? 'text-green-600' : 'text-red-600'}>
                    {sellerAccount.chargesEnabled ? 'Enabled' : 'Disabled'}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span>Payouts:</span>
                  <span className={sellerAccount.payoutsEnabled ? 'text-green-600' : 'text-red-600'}>
                    {sellerAccount.payoutsEnabled ? 'Enabled' : 'Disabled'}
                  </span>
                </div>
              </div>
            </div>
            <Button
              onClick={() => window.location.href = '/payment/connect-dashboard'}
              className="w-full"
            >
              Go to Seller Dashboard
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Show account creation form
  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <CardTitle className="flex items-center justify-center">
            <User className="h-6 w-6 mr-2" />
            Become a Seller
          </CardTitle>
          <CardDescription>
            Start accepting payments by setting up your seller account
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="space-y-2">
            <Label htmlFor="email">Email for Stripe Account</Label>
            <Input
              id="email"
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              placeholder="Enter email address"
              disabled={loading}
            />
            <p className="text-xs text-gray-500">
              This email will be used for your Stripe account
            </p>
          </div>

          {error && (
            <div className="flex items-center p-3 bg-red-50 text-red-700 rounded-lg">
              <AlertCircle className="h-4 w-4 mr-2" />
              {error}
            </div>
          )}

          <Button
            onClick={handleCreateAccount}
            disabled={loading || !email.trim()}
            className="w-full"
          >
            {loading ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Creating Account...
              </>
            ) : (
              'Create Seller Account'
            )}
          </Button>

          <div className="text-xs text-gray-500 text-center">
            By creating an account, you agree to Stripe's terms of service
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
