'use client';

import React, { useState } from 'react';
import SimplePaymentForm from '../../components/SimplePaymentForm';

export default function TestCaptureMethodsPage() {
  const [testMode, setTestMode] = useState<'automatic' | 'manual'>('automatic');
  const [paymentResult, setPaymentResult] = useState<string | null>(null);

  const handlePaymentSuccess = (paymentIntent: any) => {
    console.log('✅ Payment succeeded:', paymentIntent);
    setPaymentResult(`✅ Payment succeeded! 
      ID: ${paymentIntent.id}
      Status: ${paymentIntent.status}
      Capture Method: ${paymentIntent.capture_method}
      Amount: $${(paymentIntent.amount / 100).toFixed(2)}`);
  };

  const handlePaymentError = (error: string) => {
    console.error('❌ Payment failed:', error);
    setPaymentResult(`❌ Payment failed: ${error}`);
  };

  const testData = {
    amount: 2000, // $20.00
    currency: 'usd',
    productName: `Test Product (${testMode} capture)`,
    userId: 'test-user-123',
    sellerId: 'test-seller-456',
    orderId: `test-order-${Date.now()}`,
    isEscrow: testMode === 'manual', // Use escrow for manual capture
  };

  return (
    <div className="min-h-screen bg-gray-100 py-8">
      <div className="container mx-auto px-4">
        <h1 className="text-3xl font-bold text-center mb-8">Capture Method Testing</h1>
        
        {/* Test Mode Selector */}
        <div className="max-w-2xl mx-auto mb-8">
          <div className="bg-white p-6 rounded-lg shadow-sm border">
            <h2 className="text-lg font-semibold mb-4">Select Capture Method to Test</h2>
            <div className="grid grid-cols-2 gap-4">
              <button
                onClick={() => {
                  setTestMode('automatic');
                  setPaymentResult(null);
                }}
                className={`p-4 rounded border text-left transition-colors ${
                  testMode === 'automatic'
                    ? 'bg-blue-600 text-white border-blue-600'
                    : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'
                }`}
              >
                <div className="font-medium">Automatic Capture</div>
                <div className="text-sm mt-1 opacity-75">
                  Regular payments - funds captured immediately
                </div>
                <div className="text-xs mt-2 font-mono">
                  isEscrow: false<br/>
                  captureMethod: 'automatic'
                </div>
              </button>
              
              <button
                onClick={() => {
                  setTestMode('manual');
                  setPaymentResult(null);
                }}
                className={`p-4 rounded border text-left transition-colors ${
                  testMode === 'manual'
                    ? 'bg-green-600 text-white border-green-600'
                    : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'
                }`}
              >
                <div className="font-medium">Manual Capture</div>
                <div className="text-sm mt-1 opacity-75">
                  Escrow payments - funds authorized, captured later
                </div>
                <div className="text-xs mt-2 font-mono">
                  isEscrow: true<br/>
                  captureMethod: 'manual'
                </div>
              </button>
            </div>
          </div>
        </div>

        {/* Payment Result */}
        {paymentResult && (
          <div className="max-w-2xl mx-auto mb-8">
            <div className={`p-4 rounded border ${
              paymentResult.includes('✅') 
                ? 'bg-green-50 border-green-200 text-green-800'
                : 'bg-red-50 border-red-200 text-red-800'
            }`}>
              <h3 className="font-semibold mb-2">Payment Result:</h3>
              <pre className="text-sm whitespace-pre-wrap">{paymentResult}</pre>
              <button
                onClick={() => setPaymentResult(null)}
                className="mt-2 text-xs underline opacity-75 hover:opacity-100"
              >
                Clear
              </button>
            </div>
          </div>
        )}

        {/* Current Test Configuration */}
        <div className="max-w-2xl mx-auto mb-8">
          <div className="bg-gray-900 text-green-400 p-4 rounded font-mono text-sm">
            <div className="text-white mb-2">🧪 Current Test Configuration:</div>
            <div>Mode: {testMode}</div>
            <div>isEscrow: {testData.isEscrow.toString()}</div>
            <div>Amount: ${(testData.amount / 100).toFixed(2)}</div>
            <div>Expected captureMethod: '{testMode}'</div>
            <div>Expected endpoint: {testData.isEscrow ? '/api/escrow/create-payment-intent' : '/api/create-payment-intent'}</div>
          </div>
        </div>

        {/* Payment Form */}
        <div className="max-w-md mx-auto">
          <h2 className="text-xl font-semibold mb-4 text-center">
            Test {testMode === 'automatic' ? 'Regular' : 'Escrow'} Payment
          </h2>
          <SimplePaymentForm
            {...testData}
            onSuccess={handlePaymentSuccess}
            onError={handlePaymentError}
          />
        </div>

        {/* Instructions */}
        <div className="max-w-4xl mx-auto mt-12 p-6 bg-white rounded-lg shadow-sm border">
          <h3 className="text-lg font-semibold mb-4">Testing Instructions</h3>
          <div className="space-y-4 text-sm">
            <div>
              <h4 className="font-medium">✅ Fix Applied</h4>
              <p className="text-gray-600">
                • Elements now configured with correct captureMethod<br/>
                • Automatic capture: captureMethod: 'automatic'<br/>
                • Manual capture (escrow): captureMethod: 'manual'<br/>
                • Should resolve "capture_method mismatch" error
              </p>
            </div>
            
            <div>
              <h4 className="font-medium">1. Test Automatic Capture</h4>
              <p className="text-gray-600">
                • Click "Automatic Capture" button above<br/>
                • Use test card: 4242 4242 4242 4242<br/>
                • Should complete payment immediately<br/>
                • Check console for "captureMethod: 'automatic'"
              </p>
            </div>
            
            <div>
              <h4 className="font-medium">2. Test Manual Capture (Escrow)</h4>
              <p className="text-gray-600">
                • Click "Manual Capture" button above<br/>
                • Use test card: 4242 4242 4242 4242<br/>
                • Should authorize payment (capture later)<br/>
                • Check console for "captureMethod: 'manual'"
              </p>
            </div>
            
            <div>
              <h4 className="font-medium">3. Expected Results</h4>
              <p className="text-gray-600">
                • ✅ No "capture_method mismatch" errors<br/>
                • ✅ Both automatic and manual capture work<br/>
                • ✅ Console shows correct endpoint selection<br/>
                • ✅ Payment status matches capture method
              </p>
            </div>
            
            <div>
              <h4 className="font-medium">4. Debug Information</h4>
              <p className="text-gray-600">
                Check browser console for:<br/>
                • "⚙️ Elements configuration" - Shows captureMethod<br/>
                • "🎯 Using endpoint" - Shows correct API endpoint<br/>
                • "📄 Payment intent response data" - Shows success
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
