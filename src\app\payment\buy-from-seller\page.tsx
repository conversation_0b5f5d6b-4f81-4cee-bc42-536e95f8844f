"use client";

import { useState, useEffect, Suspense } from "react";
import SimplePaymentForm from "@/components/SimplePaymentForm";
import { useCurrentUser } from "@/hooks/useCurrentUser";
import { useSearchParams } from "next/navigation";

function BuyFromSellerContent() {
  const { user } = useCurrentUser();
  const searchParams = useSearchParams();
  const [showCheckout, setShowCheckout] = useState(false);
  // Get parameters from URL or use defaults
  const amount = parseInt(searchParams?.get("amount") || "");
  const currency = searchParams?.get("currency") || "";
  const productName = searchParams?.get("productName") || "";
  const sellerId = searchParams?.get("sellerId") || "";
  const orderId = searchParams?.get("orderId") || undefined;
  const paymentMethod =
    (searchParams?.get("paymentMethod") as "wallet" | "card" | "crypto") || "card";
  const isEscrow = paymentMethod === "wallet" || paymentMethod === "card";
  const transactionId = searchParams?.get("transactionId") || undefined;
  const paymentIntentId = searchParams?.get("paymentIntentId") || undefined;
  const sessionId = searchParams?.get("sessionId") || undefined;
  const isInvoice = searchParams?.get("isInvoice") || undefined;
  const formateDate = searchParams?.get("formateDate") || undefined;

  // Auto-show checkout if URL parameters are present
  useEffect(() => {
    if (searchParams?.get("amount")) {
      setShowCheckout(true);

      // Store order data for payment success handling
      const orderData = {
        orderId,
        transactionId,
        amount,
        currency,
        isEscrow,
        userId: user?.uid,
        sellerId,
        userEmail: user?.email,
        userName: user?.displayName || user?.email,
        productName,
      };

      localStorage.setItem("currentOrderData", JSON.stringify(orderData));
    }
  }, [
    searchParams,
    orderId,
    transactionId,
    amount,
    currency,
    isEscrow,
    user?.uid,
    sellerId,
    user?.email,
    user?.displayName,
    productName,
  ]);

  const handlePaymentSuccess = async (paymentIntent: any) => {
    console.log("Payment successful:", paymentIntent);

    try {
      // Call our payment success API to handle all database updates
      const response = await fetch("/api/payment/success", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          paymentIntentId: paymentIntent.id,
          orderId: orderId,
          transactionId: transactionId,
          amount: amount,
          currency: currency,
          isEscrow: isEscrow,
          userId: user?.uid,
          sellerId: sellerId,
          userEmail: user?.email,
          userName: user?.displayName || user?.email,
          sellerName: "Seller", // You might want to fetch this from your seller data
        }),
      });

      const result = await response.json();

      if (result.success) {
        console.log("✅ Payment processing completed successfully");
        console.log({ result });

        // Redirect to orders page to show the new order
        // const ordersUrl = `/orders?highlight=${orderId}`;
        // window.location.href = ordersUrl;
        const successUrl =
          `/payment-success?` +
          new URLSearchParams({
            payment_intent: paymentIntent.id,
            amount: amount.toString(),
            currency: currency,
            transactionId: transactionId ?? "",
            sessionId: sessionId ?? "",
            isInvoice: isInvoice ?? "",
            formateDate: formateDate ?? "",
            sellerId: sellerId ?? "",

            ///
            ...(orderId && { order_id: orderId }),
            ...(isEscrow && { escrow: "true" }),
          }).toString();

        console.log({
          payment_intent: paymentIntent.id,
          amount: amount.toString(),
          currency: currency,
          error: result.error,
          transactionId: transactionId ?? "",
          sessionId: sessionId ?? "",
          isInvoice: isInvoice ?? "",
          formateDate: formateDate ?? "",
          sellerId: sellerId ?? "",
          ///
          ...(orderId && { order_id: orderId }),
          ...(isEscrow && { escrow: "true" }),
        });

        // console.log({url:successUrl});

        window.location.href = successUrl;
        // console.log('3-->',{url:successUrl});
      } else {
        console.error("❌ Payment processing failed:", result.error);
        // Still redirect to success page but with error info
        const successUrl =
          `/payment-success?` +
          new URLSearchParams({
            payment_intent: paymentIntent.id,
            amount: amount.toString(),
            currency: currency,
            error: result.error,
            ...(orderId && { order_id: orderId }),
            ...(isEscrow && { escrow: "true" }),
          }).toString();

        window.location.href = successUrl;
        // console.log('2-->',{url:successUrl});
      }
    } catch (error) {
      console.error("❌ Error calling payment success API:", error);

      // Fallback to basic success page
      const successUrl =
        `/payment-success?` +
        new URLSearchParams({
          payment_intent: paymentIntent.id,
          amount: amount.toString(),
          currency: currency,
          error: "Processing error",
          ...(orderId && { order_id: orderId }),
          ...(isEscrow && { escrow: "true" }),
        }).toString();

      window.location.href = successUrl;
      // console.log('1-->',{url:successUrl});
    }
  };

  const handlePaymentError = (error: string) => {
    console.error("Payment error:", error);
    // You can create a proper error page later
    // alert(`Payment failed: ${error}`);
  };

  if (showCheckout) {
    return (
      <div className="px-12 mt-8">
        {/* <h1 className="text-2xl font-bold mb-6">
          {isEscrow ? "Escrow Payment" : "Buy from Seller"}
        </h1> */}

        {/* Showing only the selected method */}

        {/* Payment Forms */}
        {isEscrow ? (
          <SimplePaymentForm
            paymentIntentId={paymentIntentId}
            amount={amount}
            currency={currency}
            productName={productName}
            isEscrow={true}
            orderId={orderId}
            transactionId={transactionId}
            userId={user?.uid}
            sellerId={sellerId}
            onSuccess={handlePaymentSuccess}
            onError={handlePaymentError}
            uiMode={paymentMethod === "wallet" ? "wallet" : "card"}
          />
        ) : (
          <SimplePaymentForm
            amount={amount}
            currency={currency}
            productName={productName}
            isEscrow={false}
            orderId={orderId}
            transactionId={transactionId}
            userId={user?.uid}
            sellerId={sellerId}
            onSuccess={handlePaymentSuccess}
            onError={handlePaymentError}
            uiMode="crypto"
          />
        )}
        {/* <button
          onClick={() => setShowCheckout(false)}
          className="mt-4 text-gray-600 hover:text-gray-800"
        >
          ← Back
        </button> */}
      </div>
    );
  }

  return (
    <div className="mt-8">
      <h1 className="text-2xl font-bold mb-6">Buy from Seller</h1>
      <button
        onClick={() => setShowCheckout(true)}
        className="w-full bg-blue-600 hover:bg-blue-700 text-white font-bold py-3 px-4 rounded"
      >
        Pay {isEscrow ? "Escrow" : "Seller"} ${(amount / 100).toFixed(2)}
      </button>
    </div>
  );
}

export default function BuyFromSeller() {
  return (
    <Suspense
      fallback={
        <div className="min-h-screen flex items-center justify-center">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p>Loading payment page...</p>
          </div>
        </div>
      }
    >
      <BuyFromSellerContent />
    </Suspense>
  );
}
