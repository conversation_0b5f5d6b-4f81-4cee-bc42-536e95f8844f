'use client';

import React, { useState } from 'react';
import SimplePaymentForm from '../../components/SimplePaymentForm';
import StripeEmbeddedForm from '../../components/StripeEmbeddedForm';
import EmbeddedPaymentForm from '../../components/EmbeddedPaymentForm';
import ApplePayDiagnostics from '../../components/ApplePayDiagnostics';
import PaymentDebugger from '../../components/PaymentDebugger';

type PaymentFormType = 'simple' | 'embedded' | 'legacy' | 'diagnostics' | 'debugger';

export default function PaymentTestPage() {
  const [activeForm, setActiveForm] = useState<PaymentFormType>('simple');
  const [paymentResult, setPaymentResult] = useState<string | null>(null);

  const handlePaymentSuccess = (paymentIntent: any) => {
    console.log('✅ Payment succeeded:', paymentIntent);
    setPaymentResult(`✅ Payment succeeded! ID: ${paymentIntent.id}, Status: ${paymentIntent.status}`);
  };

  const handlePaymentError = (error: string) => {
    console.error('❌ Payment failed:', error);
    setPaymentResult(`❌ Payment failed: ${error}`);
  };

  const testData = {
    amount: 2000, // $20.00
    currency: 'usd',
    productName: 'Test Product',
    userId: 'test-user-123',
    sellerId: 'test-seller-456',
    orderId: 'test-order-789',
    isEscrow: false,
  };

  const renderActiveForm = () => {
    switch (activeForm) {
      case 'simple':
        return (
          <SimplePaymentForm
            {...testData}
            onSuccess={handlePaymentSuccess}
            onError={handlePaymentError}
          />
        );
      
      case 'embedded':
        return (
          <StripeEmbeddedForm
            {...testData}
            onSuccess={handlePaymentSuccess}
            onError={handlePaymentError}
          />
        );
      
      case 'legacy':
        return (
          <EmbeddedPaymentForm
            {...testData}
            onSuccess={handlePaymentSuccess}
            onError={handlePaymentError}
          />
        );
      
      case 'diagnostics':
        return <ApplePayDiagnostics />;

      case 'debugger':
        return <PaymentDebugger />;

      default:
        return null;
    }
  };

  return (
    <div className="min-h-screen bg-gray-100 py-8">
      <div className="container mx-auto px-4">
        <h1 className="text-3xl font-bold text-center mb-8">Payment Forms Testing</h1>
        
        {/* Form Selector */}
        <div className="max-w-4xl mx-auto mb-8">
          <div className="bg-white p-6 rounded-lg shadow-sm border">
            <h2 className="text-lg font-semibold mb-4">Select Payment Form to Test</h2>
            <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
              <button
                onClick={() => setActiveForm('simple')}
                className={`p-3 rounded border text-sm font-medium transition-colors ${
                  activeForm === 'simple'
                    ? 'bg-blue-600 text-white border-blue-600'
                    : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'
                }`}
              >
                Simple Payment Form
                <div className="text-xs mt-1 opacity-75">
                  With Apple Pay & Express Checkout
                </div>
              </button>
              
              <button
                onClick={() => setActiveForm('embedded')}
                className={`p-3 rounded border text-sm font-medium transition-colors ${
                  activeForm === 'embedded'
                    ? 'bg-blue-600 text-white border-blue-600'
                    : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'
                }`}
              >
                Stripe Embedded Form
                <div className="text-xs mt-1 opacity-75">
                  Standard Stripe Elements
                </div>
              </button>
              
              <button
                onClick={() => setActiveForm('legacy')}
                className={`p-3 rounded border text-sm font-medium transition-colors ${
                  activeForm === 'legacy'
                    ? 'bg-blue-600 text-white border-blue-600'
                    : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'
                }`}
              >
                Legacy Payment Form
                <div className="text-xs mt-1 opacity-75">
                  Older implementation
                </div>
              </button>
              
              <button
                onClick={() => setActiveForm('diagnostics')}
                className={`p-3 rounded border text-sm font-medium transition-colors ${
                  activeForm === 'diagnostics'
                    ? 'bg-green-600 text-white border-green-600'
                    : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'
                }`}
              >
                Apple Pay Diagnostics
                <div className="text-xs mt-1 opacity-75">
                  Debug & Setup
                </div>
              </button>

              <button
                onClick={() => setActiveForm('debugger')}
                className={`p-3 rounded border text-sm font-medium transition-colors ${
                  activeForm === 'debugger'
                    ? 'bg-red-600 text-white border-red-600'
                    : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'
                }`}
              >
                System Debugger
                <div className="text-xs mt-1 opacity-75">
                  Full Analysis
                </div>
              </button>
            </div>
          </div>
        </div>

        {/* Payment Result */}
        {paymentResult && (
          <div className="max-w-4xl mx-auto mb-8">
            <div className={`p-4 rounded border ${
              paymentResult.includes('✅') 
                ? 'bg-green-50 border-green-200 text-green-800'
                : 'bg-red-50 border-red-200 text-red-800'
            }`}>
              <h3 className="font-semibold mb-2">Payment Result:</h3>
              <p className="font-mono text-sm">{paymentResult}</p>
              <button
                onClick={() => setPaymentResult(null)}
                className="mt-2 text-xs underline opacity-75 hover:opacity-100"
              >
                Clear
              </button>
            </div>
          </div>
        )}

        {/* Active Form */}
        <div className="max-w-4xl mx-auto">
          {activeForm !== 'diagnostics' && activeForm !== 'debugger' && (
            <div className="mb-6 text-center">
              <h2 className="text-xl font-semibold mb-2">
                {activeForm === 'simple' && 'Simple Payment Form'}
                {activeForm === 'embedded' && 'Stripe Embedded Form'}
                {activeForm === 'legacy' && 'Legacy Payment Form'}
              </h2>
              <p className="text-gray-600 text-sm">
                Test Amount: ${(testData.amount / 100).toFixed(2)} {testData.currency.toUpperCase()}
              </p>
            </div>
          )}
          
          {renderActiveForm()}
        </div>

        {/* Instructions */}
        <div className="max-w-4xl mx-auto mt-12 p-6 bg-white rounded-lg shadow-sm border">
          <h3 className="text-lg font-semibold mb-4">Testing Instructions</h3>
          <div className="space-y-4 text-sm">
            <div>
              <h4 className="font-medium">✅ Recent Fixes Applied</h4>
              <p className="text-gray-600">
                • elements.submit() now called before confirmPayment()<br/>
                • Fixed API endpoint paths and JSON parsing<br/>
                • Enhanced Apple Pay debugging and diagnostics
              </p>
            </div>
            
            <div>
              <h4 className="font-medium">1. Apple Pay Testing</h4>
              <p className="text-gray-600">
                • Use Safari on Mac/iPhone with Touch ID/Face ID<br/>
                • Ensure cards are added to Apple Wallet<br/>
                • Domain must be registered with Stripe (use diagnostics)
              </p>
            </div>
            
            <div>
              <h4 className="font-medium">2. Card Testing</h4>
              <p className="text-gray-600">
                • Use Stripe test cards: 4242 4242 4242 4242<br/>
                • Any future expiry date and any 3-digit CVC<br/>
                • Any billing postal code
              </p>
            </div>
            
            <div>
              <h4 className="font-medium">3. Error Testing</h4>
              <p className="text-gray-600">
                • Card declined: 4000 0000 0000 0002<br/>
                • Insufficient funds: 4000 0000 0000 9995<br/>
                • Check console for detailed error logs
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
