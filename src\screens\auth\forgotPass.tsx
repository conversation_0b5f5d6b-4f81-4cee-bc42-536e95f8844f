"use client";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { sendResetPassword } from "@/services/usersServices";
import { useState } from "react";

interface ForgotPassProps {
  setForgetPass: (value: boolean) => void;
  setIsLogin: (value: boolean) => void;
}

const ForgotPass = ({ setForgetPass, setIsLogin }: ForgotPassProps) => {
  const [email, setEmail] = useState("");
  const [toggle, setToggle] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");

  const handleContinue = async () => {
    setLoading(true);
    setError("");
    try {
      const res = await sendResetPassword(email);
      if (res.success) {
        setToggle(true);
      } else {
        setError(res.error || "Failed to send reset password email");
      }
    } catch (err) {
      setError("Something went wrong");
    } finally {
      setLoading(false);
    }
  };

  const handleOkClick = () => {
    setForgetPass(false);
    // setIsLogin(false);
  };

  return (
    <>
      {!toggle ? (
        <div>
          <p className="text-center text-primary font-[600] text-2xl mb-3">Forgot password?</p>
          <p className="text-center text-borderColor">
            To recover your password, enter the email that was used for registration
          </p>
          <div className="grid w-full md:max-w-sm items-center gap-1.5 mt-6 max-md:text-start">
            <Label htmlFor="email" className="text-titleLabel font-bold text-lg">
              Email
            </Label>
            <Input
              type="email"
              id="email"
              placeholder="Email"
              className="text-primary"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
            />
          </div>

          {error && <p className="text-red-500 text-sm mt-2">{error}</p>}

          <Button
            className="rounded-full w-full border-primary btn border-[2px] text-primary py-2 hover:bg-primary hover:text-white mt-4"
            style={{ paddingTop: "10px" }}
            variant="outline"
            onClick={handleContinue}
            disabled={loading || email.length <= 4}
          >
            {loading ? "Sending..." : "Continue"}
          </Button>
        </div>
      ) : (
        <div>
          <p className="text-center text-primary font-[600] text-2xl mb-3 mt-8">Reset Email Sent</p>
          <p className="text-center text-borderColor md:px-16">
            A password reset message has been sent to your email/spam
          </p>

          <Button
            className="rounded-full w-full border-primary btn border-[2px] text-primary py-2 hover:bg-primary hover:text-white mt-8"
            style={{ paddingTop: "10px" }}
            variant="outline"
            onClick={handleOkClick}
          >
            Ok
          </Button>
        </div>
      )}
    </>
  );
};

export default ForgotPass;
