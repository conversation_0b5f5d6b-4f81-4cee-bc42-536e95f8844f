"use client";

import React, { useState, useEffect } from 'react';

interface DiagnosticResult {
  name: string;
  status: 'pass' | 'fail' | 'warning' | 'info';
  message: string;
  details?: string;
}

export default function ApplePayDiagnostics() {
  const [diagnostics, setDiagnostics] = useState<DiagnosticResult[]>([]);
  const [isRunning, setIsRunning] = useState(false);
  const [domains, setDomains] = useState<any[]>([]);
  const [currentDomain, setCurrentDomain] = useState('');

  useEffect(() => {
    setCurrentDomain(window.location.hostname);
  }, []);

  const runDiagnostics = async () => {
    setIsRunning(true);
    const results: DiagnosticResult[] = [];

    // 1. Check HTTPS
    const isHTTPS = window.location.protocol === 'https:';
    results.push({
      name: 'HTTPS Protocol',
      status: isHTTPS ? 'pass' : 'fail',
      message: isHTTPS ? 'Site is served over HTTPS' : 'Site must be served over HTTPS for Apple Pay',
      details: `Current protocol: ${window.location.protocol}`
    });

    // 2. Check Browser Support
    const isSafari = /^((?!chrome|android).)*safari/i.test(navigator.userAgent);
    const hasApplePaySession = typeof (window as any).ApplePaySession !== 'undefined';
    
    results.push({
      name: 'Browser Support',
      status: isSafari ? 'pass' : 'warning',
      message: isSafari ? 'Safari browser detected' : 'Apple Pay works best in Safari',
      details: `User Agent: ${navigator.userAgent}`
    });

    results.push({
      name: 'Apple Pay Session API',
      status: hasApplePaySession ? 'pass' : 'fail',
      message: hasApplePaySession ? 'ApplePaySession API is available' : 'ApplePaySession API not available',
      details: hasApplePaySession ? 'Apple Pay is supported on this device/browser' : 'Use Safari on a supported Apple device'
    });

    // 3. Check Apple Pay Availability
    if (hasApplePaySession) {
      try {
        const canMakePayments = (window as any).ApplePaySession.canMakePayments();
        results.push({
          name: 'Apple Pay Availability',
          status: canMakePayments ? 'pass' : 'warning',
          message: canMakePayments ? 'Apple Pay is available' : 'Apple Pay not set up on this device',
          details: canMakePayments ? 'User has Apple Pay configured' : 'User needs to add cards to Apple Wallet'
        });
      } catch (error) {
        results.push({
          name: 'Apple Pay Availability',
          status: 'fail',
          message: 'Error checking Apple Pay availability',
          details: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    }

    // 4. Check Domain Registration
    try {
      const response = await fetch('/api/stripe/list-domains');
      const data = await response.json();
      
      if (data.success) {
        setDomains(data.domains);
        const isDomainRegistered = data.domains.some((domain: any) => 
          domain.domain_name === currentDomain
        );
        
        results.push({
          name: 'Domain Registration',
          status: isDomainRegistered ? 'pass' : 'fail',
          message: isDomainRegistered 
            ? `Domain ${currentDomain} is registered with Stripe` 
            : `Domain ${currentDomain} is NOT registered with Stripe`,
          details: `Registered domains: ${data.domains.map((d: any) => d.domain_name).join(', ')}`
        });
      } else {
        results.push({
          name: 'Domain Registration',
          status: 'fail',
          message: 'Failed to check domain registration',
          details: data.error
        });
      }
    } catch (error) {
      results.push({
        name: 'Domain Registration',
        status: 'fail',
        message: 'Error checking domain registration',
        details: error instanceof Error ? error.message : 'Unknown error'
      });
    }

    // 5. Check Stripe Configuration
    const hasStripeKey = !!process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY;
    results.push({
      name: 'Stripe Configuration',
      status: hasStripeKey ? 'pass' : 'fail',
      message: hasStripeKey ? 'Stripe publishable key is configured' : 'Stripe publishable key missing',
      details: hasStripeKey ? 'Environment variable NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY is set' : 'Check your environment variables'
    });

    setDiagnostics(results);
    setIsRunning(false);
  };

  const registerCurrentDomain = async () => {
    try {
      const response = await fetch('/api/stripe/register-domain', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ domain_name: currentDomain })
      });
      
      const data = await response.json();
      
      if (data.success) {
        alert(`✅ Domain ${currentDomain} registered successfully!`);
        runDiagnostics(); // Re-run diagnostics
      } else {
        alert(`❌ Failed to register domain: ${data.error}`);
      }
    } catch (error) {
      alert(`❌ Error registering domain: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  };

  const getStatusIcon = (status: DiagnosticResult['status']) => {
    switch (status) {
      case 'pass': return '✅';
      case 'fail': return '❌';
      case 'warning': return '⚠️';
      case 'info': return 'ℹ️';
    }
  };

  const getStatusColor = (status: DiagnosticResult['status']) => {
    switch (status) {
      case 'pass': return 'text-green-700 bg-green-50 border-green-200';
      case 'fail': return 'text-red-700 bg-red-50 border-red-200';
      case 'warning': return 'text-yellow-700 bg-yellow-50 border-yellow-200';
      case 'info': return 'text-blue-700 bg-blue-50 border-blue-200';
    }
  };

  return (
    <div className="max-w-4xl mx-auto p-6 bg-white rounded-lg shadow-sm border">
      <h2 className="text-2xl font-bold mb-6">Apple Pay Diagnostics</h2>
      
      <div className="mb-6">
        <p className="text-gray-600 mb-4">
          Current domain: <strong>{currentDomain}</strong>
        </p>
        
        <div className="flex gap-4">
          <button
            onClick={runDiagnostics}
            disabled={isRunning}
            className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 disabled:bg-gray-400"
          >
            {isRunning ? 'Running Diagnostics...' : 'Run Diagnostics'}
          </button>
          
          <button
            onClick={registerCurrentDomain}
            className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700"
          >
            Register Current Domain
          </button>
        </div>
      </div>

      {diagnostics.length > 0 && (
        <div className="space-y-4">
          <h3 className="text-lg font-semibold">Diagnostic Results</h3>
          
          {diagnostics.map((result, index) => (
            <div
              key={index}
              className={`p-4 rounded border ${getStatusColor(result.status)}`}
            >
              <div className="flex items-start gap-3">
                <span className="text-xl">{getStatusIcon(result.status)}</span>
                <div className="flex-1">
                  <h4 className="font-semibold">{result.name}</h4>
                  <p className="mt-1">{result.message}</p>
                  {result.details && (
                    <p className="mt-2 text-sm opacity-75">{result.details}</p>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      {domains.length > 0 && (
        <div className="mt-8">
          <h3 className="text-lg font-semibold mb-4">Registered Domains</h3>
          <div className="bg-gray-50 p-4 rounded border">
            {domains.map((domain, index) => (
              <div key={index} className="flex justify-between items-center py-2 border-b last:border-b-0">
                <span className="font-mono">{domain.domain_name}</span>
                <span className={`px-2 py-1 rounded text-xs ${
                  domain.enabled ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                }`}>
                  {domain.enabled ? 'Enabled' : 'Disabled'}
                </span>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}
