"use client";
import ThemeSwitcher from "./themeSwitcher";
import { Badge } from "@/components/ui/badge";
import { Filter, Search, Menu, User, X } from "react-feather";
import React, { useEffect, useState, Suspense } from "react";
import { Sidebar } from "./sidebar";
import { Filter as FilterComponents } from "./filter";
import useAuth from "@/hook";
import AuthSignup from "@/screens/auth";
import { usePathname } from "next/navigation";
import { Modal, ModalBody, ModalContent } from "@heroui/react";
import Link from "next/link";
import { getUserById, getUserIdByProfileName } from "@/services/usersServices";
import { useAccount } from "wagmi";
import SearchDrawer from "./searchDrawer";
import { useFilter, FilterState } from "@/context/FilterContext";

function decodeProfileName(encoded: string): string {
  let decoded = decodeURIComponent(encoded);
  if (decoded.includes("__space__") || decoded.includes("__dash__")) {
    decoded = decoded.replace(/__space__/g, " ").replace(/__dash__/g, "-");
  } else {
    // legacy: treat dashes as spaces
    decoded = decoded.replace(/-/g, " ");
  }
  return decoded;
}

// Helper function to get logo based on category
function getCategoryLogo(category: string): string {
  const logoMap: Record<string, string> = {
    Music: "/assets/logo/Yellow.png",
    Literature: "/assets/logo/Red.png",
    Storytelling: "/assets/logo/Red.png",
    Art: "/assets/logo/Blue.png",
    "Film & Photography": "/assets/logo/green.png",
    "Theatre & Performance": "/assets/logo/Purple.png",
    Multidisciplinary: "/assets/logo/Violet.png",
    Groups: "/assets/logo/Groups.png",
    "My Feed": "/assets/logo/Customer.png",
    Customer: "/assets/logo/Customer.png",
  };
  return logoMap[category] || "/assets/logo/Default.png";
}

// Helper function to get profile URL
function getProfileUrl(user: any): string {
  if (user.isLogin) {
    return `/profile/amuzn/${user.userData?.profile_name?.replace(/\s+/g, "-")}`;
  }

  if (
    user.lensUserId &&
    user.lensData &&
    typeof user.lensData === "object" &&
    "username" in user.lensData &&
    user.lensData.username &&
    typeof user.lensData.username === "object" &&
    "localName" in user.lensData.username
  ) {
    return `/profile/lens/${(user.lensData.username as any).localName}`;
  }

  return "";
}
const NavBar = () => {
  const user = useAuth();
  const { address, isConnected } = useAccount();
  const { applyFilters, filters } = useFilter();

  // Check if filters has at least one valid value
  const hasData = Object.values(filters || {}).some((val) => {
    if (Array.isArray(val)) return val.length > 0; // check arrays
    return val !== undefined && val !== null && val !== ""; // check strings/values
  });

  const pathname = usePathname() || "";
  const isActive = pathname === "/profile";

  const [isSigninOpen, setIsSigninOpen] = useState(false);
  const [isSheetOpen, setSheetOpen] = React.useState(false);
  const [isSheetOpenFilter, setSheetOpenFilter] = React.useState(false);
  const [isSheetOpenSearch, setSheetOpenSearch] = React.useState(false);
  const pathSegments = pathname.split("/").filter(Boolean); // Remove empty segments);

  const [userCategory, setUserCategory] = useState<string | null>(null);

  const handleApplyFilter = (filters: FilterState) => {
    applyFilters(filters);
  };

  const fetchUser = async () => {
    try {
      const rawProfileName = typeof pathSegments[2] === "string" ? pathSegments[2] : "";
      const decodedProfileName = rawProfileName ? decodeProfileName(rawProfileName) : undefined;
      const profileNameToUse = decodedProfileName || user?.userData?.profile_name;

      if (!profileNameToUse) return;

      const result = await getUserIdByProfileName(profileNameToUse);
      if (!result) return;

      const response = await getUserById(result);
      if (response?.success && response?.user) {
        setUserCategory(response.user.categories?.[0]);
        return response.user.categories?.[0];
      } else {
        console.error("Invalid response structure:", response);
      }
    } catch (err) {
      console.error("Error fetching user data:", err);
    }
  };

  useEffect(() => {
    fetchUser();
  }, [pathname]);

  // Add this useEffect to listen for the openSidebar event
  useEffect(() => {
    const openSidebar = () => setSheetOpen(true);
    window.addEventListener("openSidebar", openSidebar);
    return () => window.removeEventListener("openSidebar", openSidebar);
  }, []);

  return (
    <>
      <div className="px-4 max-md:px-2">
        <div className="h-[60px] my-2 flex flex-row items-center justify-between">
          <div className="flex flex-row items-center gap-3">
            {pathSegments[0] === "browse" ? (
              <Link href="/">
                <img
                  src={getCategoryLogo(decodeURIComponent(pathSegments[1] || ""))}
                  alt="logo"
                  className="w-[60px] h-[60px] max-md:w-[46px] max-md:h-[46px]"
                  loading="lazy"
                />
              </Link>
            ) : pathSegments[0] === "profile" ? (
              <Link href="/">
                <img
                  src={getCategoryLogo(userCategory || "")}
                  alt="logo"
                  className="w-[60px] h-[60px] max-md:w-[46px] max-md:h-[46px]"
                  loading="lazy"
                />
              </Link>
            ) : (
              <Link href="/">
                <img
                  src="/assets/logo/Default.png"
                  alt="logo"
                  className="w-[60px] h-[60px] max-md:w-[46px] max-md:h-[46px]"
                  loading="lazy"
                />
              </Link>
            )}
            <div>
              <Menu
                strokeWidth={1}
                color="#333333"
                onClick={() => setSheetOpen(true)}
                className=" cursor-pointer w-[60px] h-[60px] max-md:w-[24px] max-md:h-[24px]"
              />

              <Suspense>
                <Sidebar open={isSheetOpen} onOpenChange={setSheetOpen} />
              </Suspense>
            </div>
          </div>
          <div>
            <p className="text-4xl max-md:text-xl font-bold">AMUZN</p>
          </div>
          <div className="flex flex-row items-center gap-3 max-md:gap-2">
            <Search
              size={34}
              color="#333333"
              strokeWidth={1.5}
              className="max-md:w-[24px] max-md:h-[24px ] cursor-pointer"
              onClick={() => setSheetOpenSearch(true)}
            />
            {pathSegments[0] !== "profile" && (
              <div>
                <Filter
                  size={34}
                  color="#333333"
                  fill={hasData ? "#333333" : "none"}
                  onClick={() => setSheetOpenFilter(true)}
                  className=" cursor-pointer max-md:w-[24px] max-md:h-[24px]"
                  strokeWidth={1.5}
                />

                <FilterComponents
                  open={isSheetOpenFilter}
                  onOpenChange={setSheetOpenFilter}
                  onApplyFilter={handleApplyFilter}
                />
              </div>
            )}
            {user.isLogin || address || isConnected ? (
              <Link href={getProfileUrl(user)}>
                <User
                  size={34}
                  color="#333333"
                  strokeWidth={1.5}
                  fill={isActive ? "#333333" : "none"}
                  className=" cursor-pointer max-md:w-[24px] max-md:h-[24px]"
                />
              </Link>
            ) : (
              <div className="max-md:hidden">
                <Badge
                  variant="outline"
                  className=" rounded-full btn border-primary border-2 font-semibold text-sm max-md:hidden h-[30px]  cursor-pointer px-4 "
                  onClick={() => setIsSigninOpen(true)}
                >
                  Sign In
                </Badge>
              </div>
            )}
          </div>
        </div>
        <ThemeSwitcher />
      </div>

      <div className="max-md:h-full px-5">
        <Modal
          isDismissable={false}
          isOpen={isSigninOpen}
          placement="auto"
          onOpenChange={setIsSigninOpen}
          size="lg"
          classNames={{
            closeButton: "drawer-close-button",
          }}
        >
          <ModalContent className="modal-content py-10 px-28 max-md:px-8  md:max-h-none md:overflow-visible max-md:h-full max-md:w-full max-md:-mt-[1px] max-md:overflow-scroll hide-scroll">
            {() => (
              <>
                <ModalBody className="md:overflow-visible md:max-h-none max-md:overflow-scroll h-full hide-scroll">
                  <AuthSignup onClose={() => setIsSigninOpen(false)} />
                </ModalBody>
              </>
            )}
          </ModalContent>
        </Modal>
      </div>

      {/* Search Drawer */}
      <SearchDrawer open={isSheetOpenSearch} onOpenChange={setSheetOpenSearch} />
    </>
  );
};

export default NavBar;
