"use client";

import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import type { StripeConnectInstance } from '@stripe/connect-js';
import { ConnectComponentsProvider } from '@stripe/react-connect-js';

interface ConnectEmbeddedContextType {
  stripeConnectInstance: StripeConnectInstance | null;
  isLoading: boolean;
  error: string | null;
}

const ConnectEmbeddedContext = createContext<ConnectEmbeddedContextType>({
  stripeConnectInstance: null,
  isLoading: true,
  error: null,
});

export const useConnectEmbedded = () => {
  const context = useContext(ConnectEmbeddedContext);
  if (!context) {
    throw new Error('useConnectEmbedded must be used within a ConnectEmbeddedProvider');
  }
  return context;
};

interface ConnectEmbeddedProviderProps {
  children: ReactNode;
  accountId?: string;
  components?: string[];
}

export const ConnectEmbeddedProvider: React.FC<ConnectEmbeddedProviderProps> = ({
  children,
  accountId,
  components = ['account_onboarding', 'account_management', 'payments', 'payouts']
}) => {
  const [stripeConnectInstance, setStripeConnectInstance] = useState<StripeConnectInstance | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const initializeConnect = async () => {
      try {
        setIsLoading(true);
        setError(null);

        if (!process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY) {
          throw new Error('Stripe publishable key is not configured');
        }

        // Function to fetch client secret for account sessions
        const fetchClientSecret = async () => {
          if (!accountId) {
            throw new Error('Account ID is required for Connect embedded components');
          }

          const response = await fetch('/api/connect/account-session', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              accountId,
              components,
            }),
          });

          if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.error || 'Failed to create account session');
          }

          const data = await response.json();
          return data.clientSecret;
        };

        // Dynamically import loadConnectAndInitialize to avoid SSR issues
        const { loadConnectAndInitialize } = await import('@stripe/connect-js');

        // Initialize Stripe Connect
        const connectInstance = await loadConnectAndInitialize({
          publishableKey: process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY,
          fetchClientSecret,
          appearance: {
            overlays: 'dialog',
            variables: {
              colorPrimary: '#635BFF',
              colorBackground: '#ffffff',
              colorText: '#30313d',
              colorDanger: '#df1b41',
              fontFamily: 'system-ui, sans-serif',
              spacingUnit: '4px',
              borderRadius: '8px',
            },
          },
        });

        setStripeConnectInstance(connectInstance);
      } catch (err) {
        console.error('Error initializing Stripe Connect:', err);
        setError(err instanceof Error ? err.message : 'Failed to initialize Connect');
      } finally {
        setIsLoading(false);
      }
    };

    if (accountId) {
      initializeConnect();
    } else {
      setIsLoading(false);
    }
  }, [accountId, components]);

  const value = {
    stripeConnectInstance,
    isLoading,
    error,
  };

  return (
    <ConnectEmbeddedContext.Provider value={value}>
      {stripeConnectInstance ? (
        <ConnectComponentsProvider connectInstance={stripeConnectInstance}>
          {children}
        </ConnectComponentsProvider>
      ) : (
        children
      )}
    </ConnectEmbeddedContext.Provider>
  );
};

export default ConnectEmbeddedProvider;
